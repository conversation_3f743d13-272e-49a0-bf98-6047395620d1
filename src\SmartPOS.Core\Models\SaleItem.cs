using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents an individual item in a sale transaction
/// </summary>
public class SaleItem : BaseEntity
{
    /// <summary>
    /// Sale ID this item belongs to
    /// </summary>
    public int SaleId { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Product variant ID (if applicable)
    /// </summary>
    public int? ProductVariantId { get; set; }

    /// <summary>
    /// Quantity sold
    /// </summary>
    [Column(TypeName = "decimal(18,3)")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit price at the time of sale
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Unit cost at the time of sale (for profit calculation)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Discount amount applied to this item
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal DiscountAmount { get; set; } = 0;

    /// <summary>
    /// Discount percentage applied to this item
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; } = 0;

    /// <summary>
    /// Tax amount for this item
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TaxAmount { get; set; } = 0;

    /// <summary>
    /// Tax rate applied to this item
    /// </summary>
    [Column(TypeName = "decimal(5,4)")]
    public decimal TaxRate { get; set; } = 0;

    /// <summary>
    /// Total amount for this line item (quantity * unit price - discount + tax)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Product name at the time of sale (for historical reference)
    /// </summary>
    [Required]
    [StringLength(200)]
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Product SKU at the time of sale
    /// </summary>
    [StringLength(50)]
    public string? ProductSKU { get; set; }

    /// <summary>
    /// Product barcode at the time of sale
    /// </summary>
    [StringLength(50)]
    public string? ProductBarcode { get; set; }

    /// <summary>
    /// Unit of measurement
    /// </summary>
    [StringLength(20)]
    public string Unit { get; set; } = "piece";

    /// <summary>
    /// Additional notes for this item
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Indicates whether this item was returned
    /// </summary>
    public bool IsReturned { get; set; } = false;

    /// <summary>
    /// Quantity returned (if any)
    /// </summary>
    [Column(TypeName = "decimal(18,3)")]
    public decimal ReturnedQuantity { get; set; } = 0;

    /// <summary>
    /// Date and time when item was returned
    /// </summary>
    public DateTime? ReturnedAt { get; set; }

    /// <summary>
    /// Reason for return
    /// </summary>
    [StringLength(200)]
    public string? ReturnReason { get; set; }

    /// <summary>
    /// Subtotal before discount and tax
    /// </summary>
    public decimal SubTotal => Quantity * UnitPrice;

    /// <summary>
    /// Profit for this item
    /// </summary>
    public decimal Profit => (UnitPrice - UnitCost) * Quantity - DiscountAmount;

    /// <summary>
    /// Profit margin percentage
    /// </summary>
    public decimal ProfitMargin => UnitCost > 0 ? ((UnitPrice - UnitCost) / UnitCost) * 100 : 0;

    // Navigation properties
    /// <summary>
    /// Sale this item belongs to
    /// </summary>
    public virtual Sale Sale { get; set; } = null!;

    /// <summary>
    /// Product being sold
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Product variant (if applicable)
    /// </summary>
    public virtual ProductVariant? ProductVariant { get; set; }
}
