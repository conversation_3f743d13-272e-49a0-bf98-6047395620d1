# SmartPOS Installation Guide

## Overview
This guide provides step-by-step instructions for installing SmartPOS on Windows systems. SmartPOS is designed to work on Windows 10 and Windows 11 with minimal configuration.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10 (64-bit) version 1903 or later
- **Processor**: Intel Core i3-4000 series or AMD equivalent
- **Memory**: 4 GB RAM
- **Storage**: 2 GB available disk space
- **Display**: 1024x768 resolution
- **Network**: Not required for basic operation

### Recommended Requirements
- **Operating System**: Windows 11 (64-bit)
- **Processor**: Intel Core i5-8000 series or AMD Ryzen 5 equivalent
- **Memory**: 8 GB RAM
- **Storage**: 10 GB available disk space (SSD recommended)
- **Display**: 1920x1080 resolution or higher
- **Network**: Broadband internet connection for updates and cloud features

### Hardware Compatibility
- **Barcode Scanners**: USB HID-compatible scanners
- **Receipt Printers**: ESC/POS thermal printers (80mm paper width)
- **Cash Drawers**: RJ11/RJ12 compatible with receipt printers
- **Touch Screens**: Windows-compatible touch displays

## Pre-Installation

### Prerequisites
1. **Administrator Access**: You need administrator privileges to install SmartPOS
2. **.NET Runtime**: SmartPOS requires .NET 8.0 Runtime
3. **Windows Updates**: Ensure Windows is up to date
4. **Antivirus**: Temporarily disable real-time scanning during installation

### Download .NET 8.0 Runtime
If not already installed:
1. Visit: https://dotnet.microsoft.com/download/dotnet/8.0
2. Download ".NET Desktop Runtime 8.0.x" (x64)
3. Run the installer and follow the prompts
4. Restart your computer if prompted

### Prepare Installation Environment
1. **Close Running Applications**: Close all unnecessary programs
2. **Backup Existing Data**: If upgrading, backup your current database
3. **Check Disk Space**: Ensure adequate free space on system drive
4. **Disable Antivirus**: Temporarily disable to prevent installation issues

## Installation Methods

### Method 1: Standard Installer (Recommended)

#### Step 1: Download SmartPOS Installer
1. Download `SmartPOS-Setup-v1.0.exe` from the official source
2. Verify file integrity (SHA256 checksum provided separately)
3. Save to a temporary location (e.g., Downloads folder)

#### Step 2: Run Installation
1. **Right-click** the installer file
2. Select **"Run as administrator"**
3. If Windows SmartScreen appears, click **"More info"** then **"Run anyway"**
4. The SmartPOS Setup Wizard will launch

#### Step 3: Installation Wizard
1. **Welcome Screen**: Click "Next" to continue
2. **License Agreement**: Read and accept the license terms
3. **Installation Directory**: 
   - Default: `C:\Program Files\SmartPOS`
   - Click "Browse" to change location if needed
4. **Components Selection**:
   - [x] SmartPOS Application (Required)
   - [x] Desktop Shortcut
   - [x] Start Menu Entry
   - [x] Sample Data (Optional)
5. **Ready to Install**: Review settings and click "Install"

#### Step 4: Installation Process
1. The installer will:
   - Copy application files
   - Create database directory
   - Install Windows service (if selected)
   - Create shortcuts
   - Register file associations

2. **Progress**: Monitor installation progress (typically 2-5 minutes)

#### Step 5: Complete Installation
1. **Finish Screen**: Installation complete
2. **Launch Options**:
   - [x] Launch SmartPOS now
   - [x] View Release Notes
3. Click "Finish" to complete

### Method 2: Portable Installation

#### For Portable/USB Installation
1. Download `SmartPOS-Portable-v1.0.zip`
2. Extract to desired location (USB drive, network folder, etc.)
3. Run `SmartPOS.exe` directly
4. Database will be created in the same directory

#### Portable Installation Benefits
- No administrator rights required
- Can run from USB drive
- Easy to move between computers
- No registry modifications

## Post-Installation Setup

### First Launch
1. **Launch SmartPOS** from desktop shortcut or Start menu
2. **Database Initialization**: First launch will create the database
3. **Initial Setup Wizard** will guide you through:
   - Admin account setup
   - Company information
   - Basic preferences
   - Hardware configuration

### Default Login Credentials
- **Username**: `admin`
- **Password**: `admin123`
- **⚠️ IMPORTANT**: Change these credentials immediately after first login

### Initial Configuration

#### Company Settings
1. Navigate to **Settings > Company**
2. Configure:
   - Company Name
   - Address and Contact Information
   - Tax ID and Registration Numbers
   - Logo (for receipts)

#### System Preferences
1. Go to **Settings > General**
2. Set:
   - Default Currency
   - Tax Rate
   - Language (English/Arabic)
   - Date/Time Format
   - Theme (Light/Dark)

#### Hardware Setup
1. **Receipt Printer**:
   - Connect printer via USB
   - Go to **Settings > Hardware > Printers**
   - Select printer and test print

2. **Barcode Scanner**:
   - Connect scanner via USB
   - Test scanning functionality
   - Configure scan settings if needed

## Database Configuration

### Default Database Location
- **Path**: `%APPDATA%\SmartPOS\smartpos.db`
- **Backups**: `%APPDATA%\SmartPOS\backups\`
- **Logs**: `%APPDATA%\SmartPOS\logs\`

### Custom Database Location
To use a custom database location:
1. Edit `appsettings.json` in installation directory
2. Modify connection string:
   ```json
   "ConnectionStrings": {
     "DefaultConnection": "Data Source=C:\\MyData\\smartpos.db"
   }
   ```
3. Restart SmartPOS

### Network Database (Advanced)
For multi-user scenarios:
1. Install SmartPOS on each workstation
2. Configure shared database location
3. Ensure proper network permissions
4. Test connectivity from all workstations

## Troubleshooting Installation

### Common Installation Issues

#### Issue: ".NET Runtime Not Found"
**Solution**:
1. Download .NET 8.0 Desktop Runtime
2. Install and restart computer
3. Retry SmartPOS installation

#### Issue: "Access Denied" Error
**Solution**:
1. Right-click installer
2. Select "Run as administrator"
3. Ensure user has admin privileges

#### Issue: "Installation Failed" or "Corrupted Package"
**Solution**:
1. Re-download installer
2. Verify file integrity
3. Temporarily disable antivirus
4. Clear Windows temp files
5. Retry installation

#### Issue: Application Won't Start
**Solution**:
1. Check Windows Event Viewer for errors
2. Verify .NET runtime installation
3. Run as administrator
4. Check file permissions
5. Reinstall if necessary

### Installation Logs
Installation logs are saved to:
- **Location**: `%TEMP%\SmartPOS-Setup.log`
- **Content**: Detailed installation steps and any errors
- **Use**: For troubleshooting failed installations

## Uninstallation

### Standard Uninstall
1. Open **Settings > Apps** (Windows 11) or **Control Panel > Programs** (Windows 10)
2. Find "SmartPOS" in the list
3. Click **Uninstall**
4. Follow the uninstall wizard
5. Choose to keep or remove user data

### Manual Cleanup (if needed)
If standard uninstall fails:
1. Delete installation directory: `C:\Program Files\SmartPOS`
2. Delete user data: `%APPDATA%\SmartPOS`
3. Remove desktop shortcuts
4. Clean registry entries (advanced users only)

### Data Preservation
Before uninstalling:
1. **Backup Database**: Copy `smartpos.db` file
2. **Export Settings**: Use built-in export function
3. **Save Reports**: Export important reports
4. **Document Configuration**: Note custom settings

## Upgrade Instructions

### Upgrading from Previous Version
1. **Backup Current Data**: Essential before upgrading
2. **Download New Version**: Get latest installer
3. **Run Upgrade**: Install over existing version
4. **Database Migration**: Automatic schema updates
5. **Verify Functionality**: Test all features after upgrade

### Rollback Procedure
If upgrade fails:
1. Uninstall new version
2. Reinstall previous version
3. Restore database backup
4. Verify data integrity

## Network Deployment

### Multiple Workstation Setup
1. **Server Installation**:
   - Install SmartPOS on server/main computer
   - Configure shared database location
   - Set up user accounts

2. **Client Installation**:
   - Install SmartPOS on each workstation
   - Configure to connect to server database
   - Test connectivity and permissions

3. **Network Requirements**:
   - Stable network connection
   - Proper file sharing permissions
   - Regular network backups

## Support and Resources

### Getting Help
- **User Manual**: Comprehensive usage guide
- **Admin Guide**: System administration instructions
- **Video Tutorials**: Step-by-step video guides
- **FAQ**: Common questions and answers

### Technical Support
- **Email**: <EMAIL>
- **Phone**: ******-SMARTPOS
- **Website**: www.smartpos.com/support
- **Documentation**: docs.smartpos.com

### Community Resources
- **User Forum**: community.smartpos.com
- **Knowledge Base**: kb.smartpos.com
- **Feature Requests**: feedback.smartpos.com

---

**Installation Complete!** 

Your SmartPOS system is now ready for use. Please refer to the User Manual for detailed operating instructions and the Admin Guide for system administration tasks.

*For the latest installation instructions and updates, visit: docs.smartpos.com/installation*
