using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SmartPOS.Core.Interfaces;
using SmartPOS.Core.Models;
using SmartPOS.Data.Context;
using SmartPOS.Data.Repositories;
using SmartPOS.Services.Services;
using Xunit;
using FluentAssertions;

namespace SmartPOS.Tests.Services;

public class ProductServiceTests : IDisposable
{
    private readonly SmartPOSDbContext _context;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ProductService _productService;
    private readonly Mock<ILogger<ProductService>> _loggerMock;

    public ProductServiceTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<SmartPOSDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new SmartPOSDbContext(options);
        _unitOfWork = new UnitOfWork(_context);
        _loggerMock = new Mock<ILogger<ProductService>>();
        _productService = new ProductService(_unitOfWork, _loggerMock.Object);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Add test category
        var category = new Category
        {
            Id = 1,
            Name = "Test Category",
            Description = "Test category for unit tests",
            IsActive = true
        };
        _context.Categories.Add(category);

        // Add test user
        var user = new User
        {
            Id = 1,
            Username = "testuser",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            FirstName = "Test",
            LastName = "User",
            Role = Core.Enums.UserRole.Admin,
            IsActive = true
        };
        _context.Users.Add(user);

        _context.SaveChanges();
    }

    [Fact]
    public async Task CreateProductAsync_ValidProduct_ShouldCreateSuccessfully()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            SKU = "TEST001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 100,
            MinimumStockLevel = 10,
            IsActive = true,
            CreatedById = 1
        };

        // Act
        var result = await _productService.CreateProductAsync(product);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BeGreaterThan(0);
        result.Name.Should().Be("Test Product");
        result.SKU.Should().Be("TEST001");

        // Verify product was saved to database
        var savedProduct = await _context.Products.FindAsync(result.Id);
        savedProduct.Should().NotBeNull();
        savedProduct!.Name.Should().Be("Test Product");

        // Verify inventory movement was created
        var inventoryMovement = await _context.InventoryMovements
            .FirstOrDefaultAsync(im => im.ProductId == result.Id);
        inventoryMovement.Should().NotBeNull();
        inventoryMovement!.MovementType.Should().Be(Core.Enums.InventoryMovementType.InitialStock);
        inventoryMovement.Quantity.Should().Be(100);
    }

    [Fact]
    public async Task CreateProductAsync_DuplicateSKU_ShouldThrowException()
    {
        // Arrange
        var product1 = new Product
        {
            Name = "Product 1",
            SKU = "DUPLICATE001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 50,
            IsActive = true,
            CreatedById = 1
        };

        var product2 = new Product
        {
            Name = "Product 2",
            SKU = "DUPLICATE001", // Same SKU
            CategoryId = 1,
            CostPrice = 12.00m,
            SellingPrice = 18.00m,
            CurrentStock = 30,
            IsActive = true,
            CreatedById = 1
        };

        // Act & Assert
        await _productService.CreateProductAsync(product1);
        
        var exception = await Assert.ThrowsAsync<ArgumentException>(
            () => _productService.CreateProductAsync(product2));
        
        exception.Message.Should().Contain("SKU must be unique");
    }

    [Fact]
    public async Task GetProductBySkuAsync_ExistingProduct_ShouldReturnProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "SKU Test Product",
            SKU = "SKUTEST001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 50,
            IsActive = true,
            CreatedById = 1
        };

        await _productService.CreateProductAsync(product);

        // Act
        var result = await _productService.GetProductBySkuAsync("SKUTEST001");

        // Assert
        result.Should().NotBeNull();
        result!.SKU.Should().Be("SKUTEST001");
        result.Name.Should().Be("SKU Test Product");
    }

    [Fact]
    public async Task GetProductBySkuAsync_NonExistentProduct_ShouldReturnNull()
    {
        // Act
        var result = await _productService.GetProductBySkuAsync("NONEXISTENT");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task UpdateStockAsync_ValidUpdate_ShouldUpdateStockAndCreateMovement()
    {
        // Arrange
        var product = new Product
        {
            Name = "Stock Test Product",
            SKU = "STOCK001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 100,
            MinimumStockLevel = 10,
            IsActive = true,
            CreatedById = 1
        };

        var createdProduct = await _productService.CreateProductAsync(product);
        var newStock = 150;

        // Act
        await _productService.UpdateStockAsync(createdProduct.Id, newStock, "Stock adjustment", 1);

        // Assert
        var updatedProduct = await _productService.GetProductByIdAsync(createdProduct.Id);
        updatedProduct.Should().NotBeNull();
        updatedProduct!.CurrentStock.Should().Be(newStock);

        // Verify inventory movement was created
        var movements = await _context.InventoryMovements
            .Where(im => im.ProductId == createdProduct.Id)
            .ToListAsync();
        
        movements.Should().HaveCount(2); // Initial stock + adjustment
        
        var adjustmentMovement = movements.FirstOrDefault(m => m.Reason == "Stock adjustment");
        adjustmentMovement.Should().NotBeNull();
        adjustmentMovement!.StockBefore.Should().Be(100);
        adjustmentMovement.StockAfter.Should().Be(150);
        adjustmentMovement.Quantity.Should().Be(50);
        adjustmentMovement.MovementType.Should().Be(Core.Enums.InventoryMovementType.StockIn);
    }

    [Fact]
    public async Task IsProductInStockAsync_SufficientStock_ShouldReturnTrue()
    {
        // Arrange
        var product = new Product
        {
            Name = "Stock Check Product",
            SKU = "STOCKCHECK001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 100,
            IsActive = true,
            CreatedById = 1
        };

        var createdProduct = await _productService.CreateProductAsync(product);

        // Act
        var result = await _productService.IsProductInStockAsync(createdProduct.Id, 50);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsProductInStockAsync_InsufficientStock_ShouldReturnFalse()
    {
        // Arrange
        var product = new Product
        {
            Name = "Low Stock Product",
            SKU = "LOWSTOCK001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 10,
            IsActive = true,
            CreatedById = 1
        };

        var createdProduct = await _productService.CreateProductAsync(product);

        // Act
        var result = await _productService.IsProductInStockAsync(createdProduct.Id, 50);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetLowStockProductsAsync_ShouldReturnProductsBelowMinimumLevel()
    {
        // Arrange
        var lowStockProduct = new Product
        {
            Name = "Low Stock Product",
            SKU = "LOW001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 5,
            MinimumStockLevel = 10,
            IsActive = true,
            CreatedById = 1
        };

        var normalStockProduct = new Product
        {
            Name = "Normal Stock Product",
            SKU = "NORMAL001",
            CategoryId = 1,
            CostPrice = 10.00m,
            SellingPrice = 15.00m,
            CurrentStock = 50,
            MinimumStockLevel = 10,
            IsActive = true,
            CreatedById = 1
        };

        await _productService.CreateProductAsync(lowStockProduct);
        await _productService.CreateProductAsync(normalStockProduct);

        // Act
        var result = await _productService.GetLowStockProductsAsync();

        // Assert
        result.Should().HaveCount(1);
        result.First().SKU.Should().Be("LOW001");
    }

    [Fact]
    public async Task SearchProductsAsync_WithSearchTerm_ShouldReturnMatchingProducts()
    {
        // Arrange
        var product1 = new Product
        {
            Name = "Apple iPhone",
            SKU = "IPHONE001",
            CategoryId = 1,
            CostPrice = 500.00m,
            SellingPrice = 800.00m,
            CurrentStock = 10,
            IsActive = true,
            CreatedById = 1
        };

        var product2 = new Product
        {
            Name = "Samsung Galaxy",
            SKU = "GALAXY001",
            CategoryId = 1,
            CostPrice = 400.00m,
            SellingPrice = 700.00m,
            CurrentStock = 15,
            IsActive = true,
            CreatedById = 1
        };

        await _productService.CreateProductAsync(product1);
        await _productService.CreateProductAsync(product2);

        // Act
        var result = await _productService.SearchProductsAsync("iPhone");

        // Assert
        result.Should().HaveCount(1);
        result.First().Name.Should().Be("Apple iPhone");
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
