using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a variant of a product (size, color, etc.)
/// </summary>
public class ProductVariant : BaseEntity
{
    /// <summary>
    /// Product ID this variant belongs to
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Variant name (e.g., "Large", "Red", "XL")
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Variant type (e.g., "Size", "Color", "Style")
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Variant value (e.g., "Large", "#FF0000", "Casual")
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// SKU for this specific variant
    /// </summary>
    [StringLength(50)]
    public string? SKU { get; set; }

    /// <summary>
    /// Barcode for this specific variant
    /// </summary>
    [StringLength(50)]
    public string? Barcode { get; set; }

    /// <summary>
    /// Additional cost for this variant
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AdditionalCost { get; set; } = 0;

    /// <summary>
    /// Additional price for this variant
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AdditionalPrice { get; set; } = 0;

    /// <summary>
    /// Stock quantity for this variant
    /// </summary>
    public int StockQuantity { get; set; } = 0;

    /// <summary>
    /// Weight for this variant (if different from base product)
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Dimensions for this variant (if different from base product)
    /// </summary>
    [StringLength(50)]
    public string? Dimensions { get; set; }

    /// <summary>
    /// Image path for this variant
    /// </summary>
    [StringLength(255)]
    public string? ImagePath { get; set; }

    /// <summary>
    /// Display order for sorting variants
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Indicates whether this variant is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Additional notes for this variant
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Full variant name including product name
    /// </summary>
    public string FullName => $"{Product?.Name} - {Name}";

    /// <summary>
    /// Total cost including base product cost and additional cost
    /// </summary>
    public decimal TotalCost => (Product?.CostPrice ?? 0) + AdditionalCost;

    /// <summary>
    /// Total price including base product price and additional price
    /// </summary>
    public decimal TotalPrice => (Product?.SellingPrice ?? 0) + AdditionalPrice;

    // Navigation properties
    /// <summary>
    /// Product this variant belongs to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Sale items for this variant
    /// </summary>
    public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

    /// <summary>
    /// Inventory movements for this variant
    /// </summary>
    public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
}
