using Microsoft.EntityFrameworkCore.Storage;
using SmartPOS.Core.Interfaces;
using SmartPOS.Core.Models;
using SmartPOS.Data.Context;

namespace SmartPOS.Data.Repositories;

/// <summary>
/// Unit of Work implementation for managing transactions and repositories
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly SmartPOSDbContext _context;
    private IDbContextTransaction? _transaction;
    private bool _disposed = false;

    // Repository instances
    private IRepository<User>? _users;
    private IRepository<Customer>? _customers;
    private IRepository<Category>? _categories;
    private IRepository<Product>? _products;
    private IRepository<ProductVariant>? _productVariants;
    private IRepository<Supplier>? _suppliers;
    private IRepository<SupplierProduct>? _supplierProducts;
    private IRepository<Sale>? _sales;
    private IRepository<SaleItem>? _saleItems;
    private IRepository<Payment>? _payments;
    private IRepository<InventoryMovement>? _inventoryMovements;
    private IRepository<UserSession>? _userSessions;
    private IRepository<Setting>? _settings;

    public UnitOfWork(SmartPOSDbContext context)
    {
        _context = context;
    }

    // Repository properties with lazy initialization
    public IRepository<User> Users => _users ??= new Repository<User>(_context);
    public IRepository<Customer> Customers => _customers ??= new Repository<Customer>(_context);
    public IRepository<Category> Categories => _categories ??= new Repository<Category>(_context);
    public IRepository<Product> Products => _products ??= new Repository<Product>(_context);
    public IRepository<ProductVariant> ProductVariants => _productVariants ??= new Repository<ProductVariant>(_context);
    public IRepository<Supplier> Suppliers => _suppliers ??= new Repository<Supplier>(_context);
    public IRepository<SupplierProduct> SupplierProducts => _supplierProducts ??= new Repository<SupplierProduct>(_context);
    public IRepository<Sale> Sales => _sales ??= new Repository<Sale>(_context);
    public IRepository<SaleItem> SaleItems => _saleItems ??= new Repository<SaleItem>(_context);
    public IRepository<Payment> Payments => _payments ??= new Repository<Payment>(_context);
    public IRepository<InventoryMovement> InventoryMovements => _inventoryMovements ??= new Repository<InventoryMovement>(_context);
    public IRepository<UserSession> UserSessions => _userSessions ??= new Repository<UserSession>(_context);
    public IRepository<Setting> Settings => _settings ??= new Repository<Setting>(_context);

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation)
    {
        var wasInTransaction = _transaction != null;
        
        if (!wasInTransaction)
        {
            await BeginTransactionAsync();
        }

        try
        {
            var result = await operation();
            
            if (!wasInTransaction)
            {
                await CommitTransactionAsync();
            }
            
            return result;
        }
        catch
        {
            if (!wasInTransaction)
            {
                await RollbackTransactionAsync();
            }
            throw;
        }
    }

    public async Task ExecuteInTransactionAsync(Func<Task> operation)
    {
        await ExecuteInTransactionAsync(async () =>
        {
            await operation();
            return true;
        });
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _transaction?.Dispose();
                _context.Dispose();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
