namespace SmartPOS.Core.Enums;

/// <summary>
/// Defines the status of a sale transaction
/// </summary>
public enum SaleStatus
{
    /// <summary>
    /// Sale is in progress (cart not yet completed)
    /// </summary>
    Pending = 1,

    /// <summary>
    /// Sale has been completed successfully
    /// </summary>
    Completed = 2,

    /// <summary>
    /// Sale has been cancelled
    /// </summary>
    Cancelled = 3,

    /// <summary>
    /// Sale has been refunded (full or partial)
    /// </summary>
    Refunded = 4,

    /// <summary>
    /// Sale is on hold (suspended for later completion)
    /// </summary>
    OnHold = 5,

    /// <summary>
    /// Sale has been voided (before completion)
    /// </summary>
    Voided = 6
}
