using Microsoft.Extensions.Logging;
using SmartPOS.Core.Interfaces;
using SmartPOS.Core.Models;
using SmartPOS.Core.Enums;
using System.Text;

namespace SmartPOS.Services.Services;

/// <summary>
/// Product management service implementation
/// </summary>
public class ProductService : IProductService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ProductService> _logger;
    private readonly string _imageBasePath;

    public ProductService(IUnitOfWork unitOfWork, ILogger<ProductService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _imageBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "uploads", "products");
        
        // Ensure image directory exists
        Directory.CreateDirectory(_imageBasePath);
    }

    #region Basic CRUD Operations

    public async Task<Product?> GetProductByIdAsync(int id)
    {
        try
        {
            return await _unitOfWork.Products.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product by ID {ProductId}", id);
            throw;
        }
    }

    public async Task<Product?> GetProductBySkuAsync(string sku)
    {
        try
        {
            return await _unitOfWork.Products.FirstOrDefaultAsync(p => p.SKU == sku);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product by SKU {SKU}", sku);
            throw;
        }
    }

    public async Task<Product?> GetProductByBarcodeAsync(string barcode)
    {
        try
        {
            return await _unitOfWork.Products.FirstOrDefaultAsync(p => p.Barcode == barcode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product by barcode {Barcode}", barcode);
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetAllProductsAsync()
    {
        try
        {
            return await _unitOfWork.Products.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all products");
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetActiveProductsAsync()
    {
        try
        {
            return await _unitOfWork.Products.FindAsync(p => p.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active products");
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId)
    {
        try
        {
            return await _unitOfWork.Products.FindAsync(p => p.CategoryId == categoryId && p.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting products by category {CategoryId}", categoryId);
            throw;
        }
    }

    public async Task<PagedResult<Product>> GetProductsPagedAsync(int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null)
    {
        try
        {
            return await _unitOfWork.Products.GetPagedAsync(
                pageNumber,
                pageSize,
                predicate: p => (string.IsNullOrEmpty(searchTerm) || 
                               p.Name.Contains(searchTerm) || 
                               p.SKU.Contains(searchTerm) || 
                               (p.Barcode != null && p.Barcode.Contains(searchTerm))) &&
                               (!categoryId.HasValue || p.CategoryId == categoryId.Value) &&
                               p.IsActive,
                orderBy: p => p.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged products");
            throw;
        }
    }

    public async Task<Product> CreateProductAsync(Product product)
    {
        try
        {
            // Validate product
            var validationResult = await ValidateProductAsync(product);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Product validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Generate SKU if not provided
            if (string.IsNullOrEmpty(product.SKU))
            {
                product.SKU = await GenerateSkuAsync();
            }

            var createdProduct = await _unitOfWork.Products.AddAsync(product);
            
            // Create initial inventory movement
            var inventoryMovement = new InventoryMovement
            {
                ProductId = createdProduct.Id,
                MovementType = InventoryMovementType.InitialStock,
                Quantity = product.CurrentStock,
                UnitCost = product.CostPrice,
                TotalCost = product.CurrentStock * product.CostPrice,
                StockBefore = 0,
                StockAfter = product.CurrentStock,
                Reason = "Initial stock entry",
                UserId = product.CreatedById ?? 1 // Default to admin user
            };

            await _unitOfWork.InventoryMovements.AddAsync(inventoryMovement);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product created successfully: {ProductName} (ID: {ProductId})", product.Name, createdProduct.Id);
            return createdProduct;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product: {ProductName}", product.Name);
            throw;
        }
    }

    public async Task<Product> UpdateProductAsync(Product product)
    {
        try
        {
            // Validate product
            var validationResult = await ValidateProductAsync(product);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Product validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            var updatedProduct = await _unitOfWork.Products.UpdateAsync(product);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product updated successfully: {ProductName} (ID: {ProductId})", product.Name, product.Id);
            return updatedProduct;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product: {ProductId}", product.Id);
            throw;
        }
    }

    public async Task DeleteProductAsync(int id)
    {
        try
        {
            await _unitOfWork.Products.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product deleted successfully: ID {ProductId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product: {ProductId}", id);
            throw;
        }
    }

    public async Task RestoreProductAsync(int id)
    {
        try
        {
            await _unitOfWork.Products.RestoreAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product restored successfully: ID {ProductId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring product: {ProductId}", id);
            throw;
        }
    }

    #endregion

    #region Product Variants

    public async Task<ProductVariant?> GetVariantByIdAsync(int id)
    {
        try
        {
            return await _unitOfWork.ProductVariants.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variant by ID {VariantId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ProductVariant>> GetProductVariantsAsync(int productId)
    {
        try
        {
            return await _unitOfWork.ProductVariants.FindAsync(v => v.ProductId == productId && v.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variants for product {ProductId}", productId);
            throw;
        }
    }

    public async Task<ProductVariant> CreateVariantAsync(ProductVariant variant)
    {
        try
        {
            var validationResult = await ValidateVariantAsync(variant);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Variant validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            var createdVariant = await _unitOfWork.ProductVariants.AddAsync(variant);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product variant created successfully: {VariantName} (ID: {VariantId})", variant.Name, createdVariant.Id);
            return createdVariant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product variant: {VariantName}", variant.Name);
            throw;
        }
    }

    public async Task<ProductVariant> UpdateVariantAsync(ProductVariant variant)
    {
        try
        {
            var validationResult = await ValidateVariantAsync(variant);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Variant validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            var updatedVariant = await _unitOfWork.ProductVariants.UpdateAsync(variant);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product variant updated successfully: {VariantName} (ID: {VariantId})", variant.Name, variant.Id);
            return updatedVariant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product variant: {VariantId}", variant.Id);
            throw;
        }
    }

    public async Task DeleteVariantAsync(int id)
    {
        try
        {
            await _unitOfWork.ProductVariants.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Product variant deleted successfully: ID {VariantId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product variant: {VariantId}", id);
            throw;
        }
    }

    #endregion

    #region Stock Management

    public async Task<bool> IsProductInStockAsync(int productId, decimal quantity = 1)
    {
        try
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            return product != null && product.CurrentStock >= quantity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking product stock: {ProductId}", productId);
            throw;
        }
    }

    public async Task<bool> IsVariantInStockAsync(int variantId, decimal quantity = 1)
    {
        try
        {
            var variant = await _unitOfWork.ProductVariants.GetByIdAsync(variantId);
            return variant != null && variant.StockQuantity >= quantity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking variant stock: {VariantId}", variantId);
            throw;
        }
    }

    public async Task UpdateStockAsync(int productId, int newStock, string reason, int userId)
    {
        try
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            if (product == null)
            {
                throw new ArgumentException($"Product with ID {productId} not found");
            }

            var oldStock = product.CurrentStock;
            var difference = newStock - oldStock;

            product.CurrentStock = newStock;
            await _unitOfWork.Products.UpdateAsync(product);

            // Create inventory movement
            var movement = new InventoryMovement
            {
                ProductId = productId,
                MovementType = difference > 0 ? InventoryMovementType.StockIn : InventoryMovementType.StockOut,
                Quantity = Math.Abs(difference),
                StockBefore = oldStock,
                StockAfter = newStock,
                Reason = reason,
                UserId = userId
            };

            await _unitOfWork.InventoryMovements.AddAsync(movement);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Stock updated for product {ProductId}: {OldStock} -> {NewStock}", productId, oldStock, newStock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating stock for product: {ProductId}", productId);
            throw;
        }
    }

    public async Task AdjustStockAsync(int productId, int adjustment, string reason, int userId)
    {
        try
        {
            var product = await _unitOfWork.Products.GetByIdAsync(productId);
            if (product == null)
            {
                throw new ArgumentException($"Product with ID {productId} not found");
            }

            var newStock = Math.Max(0, product.CurrentStock + adjustment);
            await UpdateStockAsync(productId, newStock, reason, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adjusting stock for product: {ProductId}", productId);
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
    {
        try
        {
            return await _unitOfWork.Products.FindAsync(p => p.IsActive && p.CurrentStock <= p.MinimumStockLevel && p.MinimumStockLevel > 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting low stock products");
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
    {
        try
        {
            return await _unitOfWork.Products.FindAsync(p => p.IsActive && p.CurrentStock <= 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting out of stock products");
            throw;
        }
    }

    #endregion

    #region Search and Filtering

    public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetActiveProductsAsync();
            }

            return await _unitOfWork.Products.FindAsync(p => 
                p.IsActive && 
                (p.Name.Contains(searchTerm) || 
                 p.SKU.Contains(searchTerm) || 
                 (p.Barcode != null && p.Barcode.Contains(searchTerm)) ||
                 (p.Description != null && p.Description.Contains(searchTerm))));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching products with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice)
    {
        try
        {
            return await _unitOfWork.Products.FindAsync(p => 
                p.IsActive && 
                p.SellingPrice >= minPrice && 
                p.SellingPrice <= maxPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting products by price range: {MinPrice} - {MaxPrice}", minPrice, maxPrice);
            throw;
        }
    }

    public async Task<IEnumerable<Product>> GetExpiringProductsAsync(int daysFromNow = 30)
    {
        try
        {
            var expiryDate = DateTime.UtcNow.AddDays(daysFromNow);
            return await _unitOfWork.Products.FindAsync(p => 
                p.IsActive && 
                p.HasExpiryDate && 
                p.DefaultExpiryDays.HasValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring products");
            throw;
        }
    }

    #endregion

    #region Helper Methods

    private async Task<string> GenerateSkuAsync()
    {
        var prefix = "PRD";
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd");
        var counter = 1;

        string sku;
        do
        {
            sku = $"{prefix}{timestamp}{counter:D4}";
            counter++;
        } while (await _unitOfWork.Products.AnyAsync(p => p.SKU == sku));

        return sku;
    }

    #endregion

    #region Validation

    public async Task<ValidationResult> ValidateProductAsync(Product product)
    {
        var result = new ValidationResult { IsValid = true };

        // Required fields
        if (string.IsNullOrWhiteSpace(product.Name))
            result.Errors.Add("Product name is required");

        if (string.IsNullOrWhiteSpace(product.SKU))
            result.Errors.Add("Product SKU is required");

        if (product.CategoryId <= 0)
            result.Errors.Add("Valid category is required");

        // Price validation
        if (product.SellingPrice < 0)
            result.Errors.Add("Selling price cannot be negative");

        if (product.CostPrice < 0)
            result.Errors.Add("Cost price cannot be negative");

        // Stock validation
        if (product.CurrentStock < 0)
            result.Errors.Add("Current stock cannot be negative");

        if (product.MinimumStockLevel < 0)
            result.Errors.Add("Minimum stock level cannot be negative");

        // Uniqueness validation
        if (!await IsSkuUniqueAsync(product.SKU, product.Id))
            result.Errors.Add("SKU must be unique");

        if (!string.IsNullOrEmpty(product.Barcode) && !await IsBarcodeUniqueAsync(product.Barcode, product.Id))
            result.Errors.Add("Barcode must be unique");

        // Category validation
        var category = await _unitOfWork.Categories.GetByIdAsync(product.CategoryId);
        if (category == null)
            result.Errors.Add("Selected category does not exist");

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    public async Task<ValidationResult> ValidateVariantAsync(ProductVariant variant)
    {
        var result = new ValidationResult { IsValid = true };

        // Required fields
        if (string.IsNullOrWhiteSpace(variant.Name))
            result.Errors.Add("Variant name is required");

        if (string.IsNullOrWhiteSpace(variant.Type))
            result.Errors.Add("Variant type is required");

        if (string.IsNullOrWhiteSpace(variant.Value))
            result.Errors.Add("Variant value is required");

        if (variant.ProductId <= 0)
            result.Errors.Add("Valid product is required");

        // Price validation
        if (variant.AdditionalPrice < 0)
            result.Errors.Add("Additional price cannot be negative");

        if (variant.AdditionalCost < 0)
            result.Errors.Add("Additional cost cannot be negative");

        // Stock validation
        if (variant.StockQuantity < 0)
            result.Errors.Add("Stock quantity cannot be negative");

        // Product validation
        var product = await _unitOfWork.Products.GetByIdAsync(variant.ProductId);
        if (product == null)
            result.Errors.Add("Selected product does not exist");

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    public async Task<bool> IsSkuUniqueAsync(string sku, int? excludeProductId = null)
    {
        try
        {
            return !await _unitOfWork.Products.AnyAsync(p => 
                p.SKU == sku && 
                (!excludeProductId.HasValue || p.Id != excludeProductId.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking SKU uniqueness: {SKU}", sku);
            throw;
        }
    }

    public async Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeProductId = null)
    {
        try
        {
            return !await _unitOfWork.Products.AnyAsync(p => 
                p.Barcode == barcode && 
                (!excludeProductId.HasValue || p.Id != excludeProductId.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking barcode uniqueness: {Barcode}", barcode);
            throw;
        }
    }

    #endregion

    #region Not Implemented Yet (Placeholder methods)

    public Task<BulkOperationResult> BulkCreateProductsAsync(IEnumerable<Product> products)
    {
        throw new NotImplementedException("Bulk create products will be implemented in next phase");
    }

    public Task<BulkOperationResult> BulkUpdateProductsAsync(IEnumerable<Product> products)
    {
        throw new NotImplementedException("Bulk update products will be implemented in next phase");
    }

    public Task<BulkOperationResult> BulkDeleteProductsAsync(IEnumerable<int> productIds)
    {
        throw new NotImplementedException("Bulk delete products will be implemented in next phase");
    }

    public Task<ImportResult> ImportProductsFromCsvAsync(Stream csvStream)
    {
        throw new NotImplementedException("CSV import will be implemented in next phase");
    }

    public Task<byte[]> ExportProductsToCsvAsync()
    {
        throw new NotImplementedException("CSV export will be implemented in next phase");
    }

    public Task<byte[]> ExportProductsToExcelAsync()
    {
        throw new NotImplementedException("Excel export will be implemented in next phase");
    }

    public Task<string> SaveProductImageAsync(int productId, Stream imageStream, string fileName)
    {
        throw new NotImplementedException("Image management will be implemented in next phase");
    }

    public Task DeleteProductImageAsync(int productId)
    {
        throw new NotImplementedException("Image management will be implemented in next phase");
    }

    public Task<string> SaveVariantImageAsync(int variantId, Stream imageStream, string fileName)
    {
        throw new NotImplementedException("Image management will be implemented in next phase");
    }

    public Task DeleteVariantImageAsync(int variantId)
    {
        throw new NotImplementedException("Image management will be implemented in next phase");
    }

    public Task<ProductStatistics> GetProductStatisticsAsync()
    {
        throw new NotImplementedException("Statistics will be implemented in next phase");
    }

    public Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
    {
        throw new NotImplementedException("Sales analytics will be implemented in next phase");
    }

    public Task<IEnumerable<Product>> GetSlowMovingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
    {
        throw new NotImplementedException("Sales analytics will be implemented in next phase");
    }

    #endregion
}
