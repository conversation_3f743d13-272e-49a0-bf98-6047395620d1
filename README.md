# SmartPOS - Comprehensive Point of Sale System

## Overview
SmartPOS is a comprehensive Windows-based Point of Sale (POS) system designed for small to medium-sized retail businesses, cafés, restaurants, gaming lounges, and specialty stores.

## Features

### Core Functionality
- **Sales & Transaction Management**
  - Fast invoice generation with barcode scanning
  - Multiple payment methods (cash, card, e-wallet, split payments)
  - Customizable receipts with shop logo integration
  - Discounts, coupons, and configurable tax calculations

- **Inventory Management System**
  - CRUD operations for products with photo support and variants
  - Real-time stock level tracking with automated reorder alerts
  - Expiry date monitoring for perishable items
  - Bulk import/export functionality via CSV/Excel formats

- **User Management**
  - Customer profile management with purchase history tracking
  - Staff role-based access control and permissions system
  - Time tracking for staff login/logout with activity logging

- **Reporting & Analytics**
  - Daily/weekly/monthly sales reports
  - Top-selling items, slow-moving inventory, and profit/loss summaries
  - Export all reports in PDF and Excel formats

## Technical Specifications
- **Frontend:** WPF (Windows Presentation Foundation) with C#
- **Database:** SQLite for offline operation with optional cloud sync
- **Framework:** .NET 8.0
- **ORM:** Entity Framework Core
- **UI Framework:** Material Design for WPF
- **Architecture:** MVVM pattern with dependency injection

## Project Structure
```
SmartPOS/
├── src/
│   ├── SmartPOS.Core/          # Core business models and interfaces
│   ├── SmartPOS.Data/          # Data access layer and Entity Framework
│   ├── SmartPOS.Services/      # Business logic and services
│   └── SmartPOS.UI/            # WPF user interface
├── tests/
│   └── SmartPOS.Tests/         # Unit and integration tests
├── docs/                       # Documentation
└── assets/                     # Icons, images, and resources
```

## Getting Started

### Prerequisites
- Windows 10/11
- .NET 8.0 SDK
- Visual Studio 2022 or Visual Studio Code

### Installation
1. Clone the repository
2. Open `SmartPOS.sln` in Visual Studio
3. Restore NuGet packages
4. Build the solution
5. Run the application

### Development Setup
```bash
# Restore packages
dotnet restore

# Build solution
dotnet build

# Run tests
dotnet test

# Run application
dotnet run --project src/SmartPOS.UI
```

## Configuration
The application uses `appsettings.json` for configuration including:
- Database connection strings
- Currency and tax settings
- Receipt printer configuration
- Localization settings
- Backup preferences

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Support
For support and documentation, please refer to the `docs/` directory or contact the development team.
