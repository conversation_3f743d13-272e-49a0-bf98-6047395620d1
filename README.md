# SmartPOS - Comprehensive Point of Sale System

<div align="center">
  <img src="assets/icons/smartpos-logo.png" alt="SmartPOS Logo" width="200"/>

  [![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/smartpos/smartpos)
  [![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/smartpos/smartpos/releases)
  [![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
  [![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)
  [![.NET](https://img.shields.io/badge/.NET-8.0-purple.svg)](https://dotnet.microsoft.com/)
</div>

## 🚀 Overview

SmartPOS is a comprehensive, offline-first Point of Sale (POS) system designed specifically for small to medium-sized retail businesses. Built with modern .NET technologies and featuring a beautiful Material Design interface, SmartPOS provides everything you need to manage your retail operations efficiently.

### 🎯 Target Businesses
- **Retail Stores** - Clothing, electronics, general merchandise
- **Cafés & Restaurants** - Food service and hospitality
- **Gaming Lounges** - PlayStation/console rental businesses
- **Specialty Stores** - Boutiques, bookstores, gift shops
- **Service Businesses** - Repair shops, salons, small services

## ✨ Key Features

### 💰 Sales & Transaction Management
- ⚡ **Lightning-fast checkout** with barcode scanning support
- 💳 **Multiple payment methods** - Cash, Card, E-wallet, Split payments
- 🧾 **Professional receipts** with customizable logos and branding
- 🏷️ **Flexible pricing** - Discounts, coupons, and configurable tax calculations
- 🔄 **Returns & refunds** with full transaction history

### 📦 Advanced Inventory Management
- 📊 **Real-time stock tracking** with automated low-stock alerts
- 🏷️ **Product variants** - Size, color, style management
- 📸 **Product photos** and detailed descriptions
- 📅 **Expiry date monitoring** for perishable items
- 📈 **Bulk operations** - Import/export via CSV and Excel
- 🔍 **Smart search** - Find products by name, SKU, or barcode

### 👥 Customer Relationship Management
- 👤 **Customer profiles** with complete purchase history
- 🎁 **Loyalty program** with points and rewards
- 📧 **Email receipts** and marketing communications
- 📊 **Customer analytics** and spending patterns
- 🌍 **Multi-language support** (English/Arabic with RTL)

### 👨‍💼 User Management & Security
- 🔐 **Role-based access control** (Admin, Manager, Cashier, Viewer)
- ⏰ **Time tracking** for staff login/logout
- 📝 **Activity logging** and audit trails
- 🔒 **Secure authentication** with password policies
- 👥 **Multi-user support** with session management

### 📊 Comprehensive Reporting
- 📈 **Sales analytics** - Daily, weekly, monthly reports
- 🏆 **Top performers** - Best-selling products and categories
- 📉 **Inventory reports** - Stock levels, movements, valuations
- 💰 **Financial summaries** - Profit/loss, tax reports
- 📄 **Export options** - PDF, Excel, and print formats

### 🖥️ Modern User Experience
- 🎨 **Material Design** interface with light/dark themes
- 📱 **Touch-friendly** design for tablets and touch screens
- 🌐 **Bilingual support** with RTL text for Arabic
- ⚡ **Responsive design** that works on various screen sizes
- 🎯 **Intuitive navigation** with context-sensitive help

## 🛠️ Technical Specifications

### Architecture
- **Frontend**: WPF (Windows Presentation Foundation) with Material Design
- **Backend**: .NET 8.0 with Entity Framework Core
- **Database**: SQLite for offline-first operation
- **Pattern**: MVVM with dependency injection
- **Testing**: xUnit with comprehensive test coverage

### System Requirements
- **OS**: Windows 10 (1903+) or Windows 11
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB minimum, 10GB recommended
- **Display**: 1024x768 minimum, 1920x1080 recommended
- **.NET**: 8.0 Desktop Runtime (included in installer)

### Hardware Compatibility
- 🖨️ **Receipt Printers**: ESC/POS thermal printers (80mm)
- 📷 **Barcode Scanners**: USB HID-compatible scanners
- 💰 **Cash Drawers**: RJ11/RJ12 compatible
- 📱 **Touch Screens**: Windows-compatible touch displays

## 📁 Project Structure

```
SmartPOS/
├── 📂 src/
│   ├── 📂 SmartPOS.Core/          # 🎯 Domain models and interfaces
│   ├── 📂 SmartPOS.Data/          # 🗄️ Data access layer with EF Core
│   ├── 📂 SmartPOS.Services/      # 🔧 Business logic and services
│   └── 📂 SmartPOS.UI/            # 🖥️ WPF user interface
├── 📂 tests/
│   └── 📂 SmartPOS.Tests/         # 🧪 Unit and integration tests
├── 📂 docs/                       # 📚 Comprehensive documentation
│   ├── 📄 UserManual.md           # 👤 End-user guide
│   ├── 📄 AdminGuide.md           # 👨‍💼 Administrator manual
│   ├── 📄 Installation.md         # 🔧 Installation instructions
│   └── 📄 Architecture.md         # 🏗️ Technical architecture
├── 📂 assets/                     # 🎨 Icons, images, and resources
└── 📄 SmartPOS.sln               # 🔨 Visual Studio solution
```

## 🚀 Quick Start

### For End Users

#### Option 1: Download Installer (Recommended)
1. 📥 Download `SmartPOS-Setup-v1.0.exe` from [Releases](https://github.com/smartpos/smartpos/releases)
2. 🔧 Run installer as administrator
3. 🚀 Launch SmartPOS from desktop shortcut
4. 🔑 Login with default credentials: `admin` / `admin123`
5. ⚙️ Follow the setup wizard to configure your store

#### Option 2: Portable Version
1. 📥 Download `SmartPOS-Portable-v1.0.zip`
2. 📂 Extract to desired location
3. 🚀 Run `SmartPOS.exe` directly
4. 💾 Database will be created in the same folder

### For Developers

#### Prerequisites
- 🔧 [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- 💻 [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)
- 🗄️ [Git](https://git-scm.com/)

#### Development Setup
```bash
# Clone the repository
git clone https://github.com/smartpos/smartpos.git
cd smartpos

# Restore NuGet packages
dotnet restore

# Build the solution
dotnet build

# Run tests
dotnet test

# Start the application
dotnet run --project src/SmartPOS.UI
```

#### Quick Build Scripts
```bash
# Windows
build.bat          # Build and test
run.bat            # Run application

# Cross-platform
dotnet build       # Build solution
dotnet test        # Run tests
```

## 📖 Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| 📄 [User Manual](docs/UserManual.md) | Complete guide for daily operations | End Users |
| 👨‍💼 [Admin Guide](docs/AdminGuide.md) | System administration and maintenance | Administrators |
| 🔧 [Installation Guide](docs/Installation.md) | Step-by-step installation instructions | IT Staff |
| 🏗️ [Architecture Guide](docs/Architecture.md) | Technical architecture and design | Developers |

## 🔧 Configuration

SmartPOS uses `appsettings.json` for configuration:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=smartpos.db"
  },
  "AppSettings": {
    "Currency": { "Code": "USD", "Symbol": "$" },
    "Tax": { "DefaultRate": 0.15 },
    "Language": "en",
    "Theme": "Light"
  }
}
```

## 🧪 Testing

SmartPOS includes comprehensive test coverage:

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. 🍴 Fork the repository
2. 🌿 Create a feature branch (`git checkout -b feature/amazing-feature`)
3. 💻 Make your changes
4. ✅ Add tests for new functionality
5. 🧪 Ensure all tests pass
6. 📝 Commit your changes (`git commit -m 'Add amazing feature'`)
7. 📤 Push to the branch (`git push origin feature/amazing-feature`)
8. 🔄 Open a Pull Request

### Code Standards
- 📏 Follow C# coding conventions
- 📝 Add XML documentation for public APIs
- 🧪 Write unit tests for new features
- 🔍 Use meaningful commit messages

## 📋 Roadmap

### Version 1.1 (Q2 2024)
- [ ] 🌐 Web-based admin panel
- [ ] 📱 Mobile companion app
- [ ] ☁️ Cloud synchronization
- [ ] 📊 Advanced analytics dashboard

### Version 1.2 (Q3 2024)
- [ ] 🔌 Plugin system
- [ ] 🛒 E-commerce integration
- [ ] 📧 Email marketing tools
- [ ] 🔔 Push notifications

### Version 2.0 (Q4 2024)
- [ ] 🌍 Multi-store management
- [ ] 🤖 AI-powered insights
- [ ] 📱 Customer mobile app
- [ ] 🔗 Third-party integrations

## 📞 Support & Community

### Getting Help
- 📚 **Documentation**: [docs.smartpos.com](https://docs.smartpos.com)
- 💬 **Community Forum**: [community.smartpos.com](https://community.smartpos.com)
- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/smartpos/smartpos/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/smartpos/smartpos/discussions)

### Professional Support
- 📧 **Email**: <EMAIL>
- 📞 **Phone**: ******-SMARTPOS
- 🌐 **Website**: [www.smartpos.com](https://www.smartpos.com)
- 📅 **Training**: Available upon request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- 🎨 [Material Design in XAML](http://materialdesigninxaml.net/) for the beautiful UI components
- 🗄️ [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/) for data access
- 🧪 [xUnit](https://xunit.net/) for testing framework
- 🔧 [AutoMapper](https://automapper.org/) for object mapping
- ✅ [FluentValidation](https://fluentvalidation.net/) for validation

## 📊 Project Stats

![GitHub stars](https://img.shields.io/github/stars/smartpos/smartpos?style=social)
![GitHub forks](https://img.shields.io/github/forks/smartpos/smartpos?style=social)
![GitHub issues](https://img.shields.io/github/issues/smartpos/smartpos)
![GitHub pull requests](https://img.shields.io/github/issues-pr/smartpos/smartpos)
![GitHub last commit](https://img.shields.io/github/last-commit/smartpos/smartpos)

---

<div align="center">
  <p>Made with ❤️ for the retail community</p>
  <p>© 2024 SmartPOS. All rights reserved.</p>
</div>
