using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmartPOS.Data.Context;

namespace SmartPOS.Data.Migrations;

/// <summary>
/// Service for managing database migrations and versioning
/// </summary>
public class DatabaseMigrationService
{
    private readonly SmartPOSDbContext _context;
    private readonly ILogger<DatabaseMigrationService> _logger;

    public DatabaseMigrationService(SmartPOSDbContext context, ILogger<DatabaseMigrationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Initialize the database and apply migrations
    /// </summary>
    /// <returns>True if successful, false otherwise</returns>
    public async Task<bool> InitializeDatabaseAsync()
    {
        try
        {
            _logger.LogInformation("Starting database initialization...");

            // Ensure database is created
            var created = await _context.Database.EnsureCreatedAsync();
            if (created)
            {
                _logger.LogInformation("Database created successfully");
            }

            // Check for pending migrations
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"Applying {pendingMigrations.Count()} pending migrations...");
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Migrations applied successfully");
            }
            else
            {
                _logger.LogInformation("Database is up to date");
            }

            // Verify database integrity
            await VerifyDatabaseIntegrityAsync();

            _logger.LogInformation("Database initialization completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database initialization failed");
            return false;
        }
    }

    /// <summary>
    /// Get current database version
    /// </summary>
    /// <returns>Current database version</returns>
    public async Task<string> GetDatabaseVersionAsync()
    {
        try
        {
            var appliedMigrations = await _context.Database.GetAppliedMigrationsAsync();
            return appliedMigrations.LastOrDefault() ?? "Initial";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get database version");
            return "Unknown";
        }
    }

    /// <summary>
    /// Check if database needs migration
    /// </summary>
    /// <returns>True if migration is needed</returns>
    public async Task<bool> IsMigrationNeededAsync()
    {
        try
        {
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            return pendingMigrations.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check migration status");
            return false;
        }
    }

    /// <summary>
    /// Create database backup before migration
    /// </summary>
    /// <param name="backupPath">Path to save backup</param>
    /// <returns>True if successful</returns>
    public async Task<bool> CreateBackupAsync(string backupPath)
    {
        try
        {
            _logger.LogInformation($"Creating database backup at {backupPath}");

            // For SQLite, we can simply copy the database file
            var connectionString = _context.Database.GetConnectionString();
            if (connectionString?.Contains("Data Source=") == true)
            {
                var dbPath = connectionString.Split("Data Source=")[1].Split(';')[0];
                if (File.Exists(dbPath))
                {
                    File.Copy(dbPath, backupPath, true);
                    _logger.LogInformation("Database backup created successfully");
                    return true;
                }
            }

            _logger.LogWarning("Could not create database backup - database file not found");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create database backup");
            return false;
        }
    }

    /// <summary>
    /// Restore database from backup
    /// </summary>
    /// <param name="backupPath">Path to backup file</param>
    /// <returns>True if successful</returns>
    public async Task<bool> RestoreBackupAsync(string backupPath)
    {
        try
        {
            _logger.LogInformation($"Restoring database from backup {backupPath}");

            if (!File.Exists(backupPath))
            {
                _logger.LogError("Backup file not found");
                return false;
            }

            var connectionString = _context.Database.GetConnectionString();
            if (connectionString?.Contains("Data Source=") == true)
            {
                var dbPath = connectionString.Split("Data Source=")[1].Split(';')[0];
                
                // Close all connections
                await _context.Database.CloseConnectionAsync();
                
                // Copy backup over current database
                File.Copy(backupPath, dbPath, true);
                
                _logger.LogInformation("Database restored successfully");
                return true;
            }

            _logger.LogError("Could not restore database - invalid connection string");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to restore database backup");
            return false;
        }
    }

    /// <summary>
    /// Verify database integrity
    /// </summary>
    private async Task VerifyDatabaseIntegrityAsync()
    {
        try
        {
            _logger.LogInformation("Verifying database integrity...");

            // Check if essential tables exist and have data
            var userCount = await _context.Users.CountAsync();
            var categoryCount = await _context.Categories.CountAsync();
            var settingCount = await _context.Settings.CountAsync();

            _logger.LogInformation($"Database integrity check: Users={userCount}, Categories={categoryCount}, Settings={settingCount}");

            // Ensure default admin user exists
            if (userCount == 0)
            {
                _logger.LogWarning("No users found in database - this may indicate a problem");
            }

            // Ensure default category exists
            if (categoryCount == 0)
            {
                _logger.LogWarning("No categories found in database - this may indicate a problem");
            }

            // Ensure settings exist
            if (settingCount == 0)
            {
                _logger.LogWarning("No settings found in database - this may indicate a problem");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database integrity verification failed");
            throw;
        }
    }

    /// <summary>
    /// Reset database to initial state (for development/testing)
    /// </summary>
    /// <returns>True if successful</returns>
    public async Task<bool> ResetDatabaseAsync()
    {
        try
        {
            _logger.LogWarning("Resetting database to initial state...");

            // Delete database
            await _context.Database.EnsureDeletedAsync();
            
            // Recreate database
            await _context.Database.EnsureCreatedAsync();
            
            _logger.LogInformation("Database reset completed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset database");
            return false;
        }
    }

    /// <summary>
    /// Get database statistics
    /// </summary>
    /// <returns>Database statistics</returns>
    public async Task<DatabaseStatistics> GetDatabaseStatisticsAsync()
    {
        try
        {
            var stats = new DatabaseStatistics
            {
                UserCount = await _context.Users.CountAsync(),
                CustomerCount = await _context.Customers.CountAsync(),
                ProductCount = await _context.Products.CountAsync(),
                CategoryCount = await _context.Categories.CountAsync(),
                SaleCount = await _context.Sales.CountAsync(),
                SupplierCount = await _context.Suppliers.CountAsync(),
                SettingCount = await _context.Settings.CountAsync(),
                LastBackupDate = GetLastBackupDate(),
                DatabaseSize = GetDatabaseSize()
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get database statistics");
            return new DatabaseStatistics();
        }
    }

    private DateTime? GetLastBackupDate()
    {
        try
        {
            var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "backups");
            if (Directory.Exists(backupDir))
            {
                var backupFiles = Directory.GetFiles(backupDir, "*.db")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .FirstOrDefault();

                return backupFiles?.CreationTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get last backup date");
        }

        return null;
    }

    private long GetDatabaseSize()
    {
        try
        {
            var connectionString = _context.Database.GetConnectionString();
            if (connectionString?.Contains("Data Source=") == true)
            {
                var dbPath = connectionString.Split("Data Source=")[1].Split(';')[0];
                if (File.Exists(dbPath))
                {
                    return new FileInfo(dbPath).Length;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get database size");
        }

        return 0;
    }
}

/// <summary>
/// Database statistics model
/// </summary>
public class DatabaseStatistics
{
    public int UserCount { get; set; }
    public int CustomerCount { get; set; }
    public int ProductCount { get; set; }
    public int CategoryCount { get; set; }
    public int SaleCount { get; set; }
    public int SupplierCount { get; set; }
    public int SettingCount { get; set; }
    public DateTime? LastBackupDate { get; set; }
    public long DatabaseSize { get; set; }
}
