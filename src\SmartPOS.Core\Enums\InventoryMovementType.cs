namespace SmartPOS.Core.Enums;

/// <summary>
/// Defines the types of inventory movements
/// </summary>
public enum InventoryMovementType
{
    /// <summary>
    /// Stock added to inventory (purchase, return, adjustment)
    /// </summary>
    StockIn = 1,

    /// <summary>
    /// Stock removed from inventory (sale, damage, theft)
    /// </summary>
    StockOut = 2,

    /// <summary>
    /// Manual adjustment to stock levels
    /// </summary>
    Adjustment = 3,

    /// <summary>
    /// Stock transfer between locations
    /// </summary>
    Transfer = 4,

    /// <summary>
    /// Initial stock entry
    /// </summary>
    InitialStock = 5,

    /// <summary>
    /// Stock damaged or expired
    /// </summary>
    Damage = 6,

    /// <summary>
    /// Stock lost or stolen
    /// </summary>
    Loss = 7
}
