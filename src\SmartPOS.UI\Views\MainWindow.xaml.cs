using System.Windows;
using System.Windows.Threading;

namespace SmartPOS.UI.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly DispatcherTimer _timer;

    public MainWindow()
    {
        InitializeComponent();
        
        // Initialize timer for current date/time display
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timer.Tick += Timer_Tick;
        _timer.Start();
        
        // Set initial data context
        DataContext = new MainWindowViewModel();
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.CurrentDateTime = DateTime.Now;
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        base.OnClosed(e);
    }
}

/// <summary>
/// Simple view model for MainWindow
/// </summary>
public class MainWindowViewModel
{
    public DateTime CurrentDateTime { get; set; } = DateTime.Now;
}
