using SmartPOS.Core.Models;

namespace SmartPOS.Core.Interfaces;

/// <summary>
/// Unit of Work pattern interface for managing transactions and repositories
/// </summary>
public interface IUnitOfWork : IDisposable
{
    // Repository properties
    IRepository<User> Users { get; }
    IRepository<Customer> Customers { get; }
    IRepository<Category> Categories { get; }
    IRepository<Product> Products { get; }
    IRepository<ProductVariant> ProductVariants { get; }
    IRepository<Supplier> Suppliers { get; }
    IRepository<SupplierProduct> SupplierProducts { get; }
    IRepository<Sale> Sales { get; }
    IRepository<SaleItem> SaleItems { get; }
    IRepository<Payment> Payments { get; }
    IRepository<InventoryMovement> InventoryMovements { get; }
    IRepository<UserSession> UserSessions { get; }
    IRepository<Setting> Settings { get; }

    /// <summary>
    /// Save all changes to the database
    /// </summary>
    /// <returns>Number of affected records</returns>
    Task<int> SaveChangesAsync();

    /// <summary>
    /// Begin a database transaction
    /// </summary>
    /// <returns>Transaction object</returns>
    Task BeginTransactionAsync();

    /// <summary>
    /// Commit the current transaction
    /// </summary>
    /// <returns>Task</returns>
    Task CommitTransactionAsync();

    /// <summary>
    /// Rollback the current transaction
    /// </summary>
    /// <returns>Task</returns>
    Task RollbackTransactionAsync();

    /// <summary>
    /// Execute a function within a transaction
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <returns>Result of the operation</returns>
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation);

    /// <summary>
    /// Execute an action within a transaction
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <returns>Task</returns>
    Task ExecuteInTransactionAsync(Func<Task> operation);
}
