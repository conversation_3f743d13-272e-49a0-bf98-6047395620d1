using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SmartPOS.Core.Enums;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a sales transaction
/// </summary>
public class Sale : BaseEntity
{
    /// <summary>
    /// Unique sale number for reference
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SaleNumber { get; set; } = string.Empty;

    /// <summary>
    /// Customer ID (optional for walk-in customers)
    /// </summary>
    public int? CustomerId { get; set; }

    /// <summary>
    /// User ID who processed this sale
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// Date and time of the sale
    /// </summary>
    public DateTime SaleDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Subtotal amount (before tax and discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Total discount amount
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal DiscountAmount { get; set; } = 0;

    /// <summary>
    /// Discount percentage applied
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; } = 0;

    /// <summary>
    /// Tax amount
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TaxAmount { get; set; } = 0;

    /// <summary>
    /// Tax rate applied
    /// </summary>
    [Column(TypeName = "decimal(5,4)")]
    public decimal TaxRate { get; set; } = 0;

    /// <summary>
    /// Total amount (subtotal - discount + tax)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Amount paid by customer
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// Change given to customer
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal ChangeAmount { get; set; } = 0;

    /// <summary>
    /// Status of the sale
    /// </summary>
    public SaleStatus Status { get; set; } = SaleStatus.Pending;

    /// <summary>
    /// Primary payment method used
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

    /// <summary>
    /// Reference number for card/electronic payments
    /// </summary>
    [StringLength(100)]
    public string? PaymentReference { get; set; }

    /// <summary>
    /// Coupon code applied (if any)
    /// </summary>
    [StringLength(50)]
    public string? CouponCode { get; set; }

    /// <summary>
    /// Additional notes for the sale
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Receipt number for printing
    /// </summary>
    [StringLength(50)]
    public string? ReceiptNumber { get; set; }

    /// <summary>
    /// Indicates whether receipt was printed
    /// </summary>
    public bool IsReceiptPrinted { get; set; } = false;

    /// <summary>
    /// Date and time when receipt was printed
    /// </summary>
    public DateTime? ReceiptPrintedAt { get; set; }

    /// <summary>
    /// Indicates whether this is a refund transaction
    /// </summary>
    public bool IsRefund { get; set; } = false;

    /// <summary>
    /// Original sale ID if this is a refund
    /// </summary>
    public int? OriginalSaleId { get; set; }

    /// <summary>
    /// Loyalty points earned from this sale
    /// </summary>
    public int LoyaltyPointsEarned { get; set; } = 0;

    /// <summary>
    /// Loyalty points redeemed in this sale
    /// </summary>
    public int LoyaltyPointsRedeemed { get; set; } = 0;

    // Navigation properties
    /// <summary>
    /// Customer who made this purchase
    /// </summary>
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// User who processed this sale
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// Items in this sale
    /// </summary>
    public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

    /// <summary>
    /// Payments for this sale (for split payments)
    /// </summary>
    public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

    /// <summary>
    /// Original sale if this is a refund
    /// </summary>
    public virtual Sale? OriginalSale { get; set; }

    /// <summary>
    /// Refund transactions for this sale
    /// </summary>
    public virtual ICollection<Sale> Refunds { get; set; } = new List<Sale>();
}
