<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Primary Colors -->
    <Color x:Key="PrimaryColor">#2196F3</Color>
    <Color x:Key="PrimaryDarkColor">#1976D2</Color>
    <Color x:Key="PrimaryLightColor">#BBDEFB</Color>

    <!-- Secondary Colors -->
    <Color x:Key="SecondaryColor">#4CAF50</Color>
    <Color x:Key="SecondaryDarkColor">#388E3C</Color>
    <Color x:Key="SecondaryLightColor">#C8E6C9</Color>

    <!-- Accent Colors -->
    <Color x:Key="AccentColor">#FF9800</Color>
    <Color x:Key="AccentDarkColor">#F57C00</Color>
    <Color x:Key="AccentLightColor">#FFE0B2</Color>

    <!-- Status Colors -->
    <Color x:Key="SuccessColor">#4CAF50</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="InfoColor">#2196F3</Color>

    <!-- Neutral Colors -->
    <Color x:Key="BackgroundColor">#FAFAFA</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="TextPrimaryColor">#212121</Color>
    <Color x:Key="TextSecondaryColor">#757575</Color>
    <Color x:Key="DividerColor">#E0E0E0</Color>

    <!-- Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>

    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource SecondaryDarkColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryLightColor}"/>

    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>

    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>

    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>

</ResourceDictionary>
