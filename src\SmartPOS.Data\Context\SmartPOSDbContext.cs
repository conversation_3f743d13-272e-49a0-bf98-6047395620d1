using Microsoft.EntityFrameworkCore;
using SmartPOS.Core.Models;

namespace SmartPOS.Data.Context;

/// <summary>
/// Entity Framework database context for SmartPOS
/// </summary>
public class SmartPOSDbContext : DbContext
{
    public SmartPOSDbContext(DbContextOptions<SmartPOSDbContext> options) : base(options)
    {
    }

    // DbSets for all entities
    public DbSet<User> Users { get; set; }
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<ProductVariant> ProductVariants { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<SupplierProduct> SupplierProducts { get; set; }
    public DbSet<Sale> Sales { get; set; }
    public DbSet<SaleItem> SaleItems { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<InventoryMovement> InventoryMovements { get; set; }
    public DbSet<UserSession> UserSessions { get; set; }
    public DbSet<Setting> Settings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureUserEntity(modelBuilder);
        ConfigureCustomerEntity(modelBuilder);
        ConfigureCategoryEntity(modelBuilder);
        ConfigureProductEntity(modelBuilder);
        ConfigureProductVariantEntity(modelBuilder);
        ConfigureSupplierEntity(modelBuilder);
        ConfigureSupplierProductEntity(modelBuilder);
        ConfigureSaleEntity(modelBuilder);
        ConfigureSaleItemEntity(modelBuilder);
        ConfigurePaymentEntity(modelBuilder);
        ConfigureInventoryMovementEntity(modelBuilder);
        ConfigureUserSessionEntity(modelBuilder);
        ConfigureSettingEntity(modelBuilder);

        // Configure global query filters for soft delete
        ConfigureSoftDeleteFilters(modelBuilder);

        // Seed initial data
        SeedInitialData(modelBuilder);
    }

    private void ConfigureUserEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => e.Username).IsUnique();
            entity.HasIndex(e => e.Email).IsUnique();
            
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
        });
    }

    private void ConfigureCustomerEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasIndex(e => e.Email).IsUnique().HasFilter("[Email] IS NOT NULL");
            entity.HasIndex(e => e.PhoneNumber).HasFilter("[PhoneNumber] IS NOT NULL");
            
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
            entity.Property(e => e.TotalSpent).HasColumnType("decimal(18,2)");
        });
    }

    private void ConfigureCategoryEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Category>(entity =>
        {
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            
            entity.HasOne(e => e.ParentCategory)
                .WithMany(e => e.SubCategories)
                .HasForeignKey(e => e.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureProductEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasIndex(e => e.SKU).IsUnique();
            entity.HasIndex(e => e.Barcode).IsUnique().HasFilter("[Barcode] IS NOT NULL");
            
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.SKU).IsRequired().HasMaxLength(50);
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.SellingPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(5,4)");
            
            entity.HasOne(e => e.Category)
                .WithMany(e => e.Products)
                .HasForeignKey(e => e.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureProductVariantEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ProductVariant>(entity =>
        {
            entity.HasIndex(e => e.SKU).IsUnique().HasFilter("[SKU] IS NOT NULL");
            entity.HasIndex(e => e.Barcode).IsUnique().HasFilter("[Barcode] IS NOT NULL");
            
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Value).IsRequired().HasMaxLength(100);
            entity.Property(e => e.AdditionalCost).HasColumnType("decimal(18,2)");
            entity.Property(e => e.AdditionalPrice).HasColumnType("decimal(18,2)");
            
            entity.HasOne(e => e.Product)
                .WithMany(e => e.Variants)
                .HasForeignKey(e => e.ProductId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSupplierEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.CreditLimit).HasColumnType("decimal(18,2)");
            entity.Property(e => e.CurrentBalance).HasColumnType("decimal(18,2)");
        });
    }

    private void ConfigureSupplierProductEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<SupplierProduct>(entity =>
        {
            entity.HasIndex(e => new { e.SupplierId, e.ProductId }).IsUnique();
            
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.LastCostPrice).HasColumnType("decimal(18,2)");
            
            entity.HasOne(e => e.Supplier)
                .WithMany(e => e.SupplierProducts)
                .HasForeignKey(e => e.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Product)
                .WithMany(e => e.SupplierProducts)
                .HasForeignKey(e => e.ProductId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSaleEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Sale>(entity =>
        {
            entity.HasIndex(e => e.SaleNumber).IsUnique();
            entity.HasIndex(e => e.SaleDate);
            entity.HasIndex(e => e.Status);
            
            entity.Property(e => e.SaleNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(5,4)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ChangeAmount).HasColumnType("decimal(18,2)");
            
            entity.HasOne(e => e.Customer)
                .WithMany(e => e.Sales)
                .HasForeignKey(e => e.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);
                
            entity.HasOne(e => e.User)
                .WithMany(e => e.Sales)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            entity.HasOne(e => e.OriginalSale)
                .WithMany(e => e.Refunds)
                .HasForeignKey(e => e.OriginalSaleId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureSaleItemEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<SaleItem>(entity =>
        {
            entity.Property(e => e.Quantity).HasColumnType("decimal(18,3)");
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.UnitCost).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(5,4)");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ReturnedQuantity).HasColumnType("decimal(18,3)");
            entity.Property(e => e.ProductName).IsRequired().HasMaxLength(200);
            
            entity.HasOne(e => e.Sale)
                .WithMany(e => e.SaleItems)
                .HasForeignKey(e => e.SaleId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Product)
                .WithMany(e => e.SaleItems)
                .HasForeignKey(e => e.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
                
            entity.HasOne(e => e.ProductVariant)
                .WithMany(e => e.SaleItems)
                .HasForeignKey(e => e.ProductVariantId)
                .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigurePaymentEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Payment>(entity =>
        {
            entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ExchangeRate).HasColumnType("decimal(18,6)");
            entity.Property(e => e.RefundedAmount).HasColumnType("decimal(18,2)");
            
            entity.HasOne(e => e.Sale)
                .WithMany(e => e.Payments)
                .HasForeignKey(e => e.SaleId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureInventoryMovementEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<InventoryMovement>(entity =>
        {
            entity.HasIndex(e => e.MovementDate);
            entity.HasIndex(e => e.MovementType);
            
            entity.Property(e => e.Quantity).HasColumnType("decimal(18,3)");
            entity.Property(e => e.UnitCost).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TotalCost).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Reason).IsRequired().HasMaxLength(200);
            
            entity.HasOne(e => e.Product)
                .WithMany(e => e.InventoryMovements)
                .HasForeignKey(e => e.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
                
            entity.HasOne(e => e.ProductVariant)
                .WithMany(e => e.InventoryMovements)
                .HasForeignKey(e => e.ProductVariantId)
                .OnDelete(DeleteBehavior.SetNull);
                
            entity.HasOne(e => e.Supplier)
                .WithMany(e => e.InventoryMovements)
                .HasForeignKey(e => e.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);
                
            entity.HasOne(e => e.Sale)
                .WithMany()
                .HasForeignKey(e => e.SaleId)
                .OnDelete(DeleteBehavior.SetNull);
                
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureUserSessionEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UserSession>(entity =>
        {
            entity.HasIndex(e => e.LoginTime);
            entity.HasIndex(e => e.IsActive);
            
            entity.HasOne(e => e.User)
                .WithMany(e => e.Sessions)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSettingEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasIndex(e => e.Key).IsUnique();
            entity.HasIndex(e => e.Category);
            
            entity.Property(e => e.Key).IsRequired().HasMaxLength(100);
            
            entity.HasOne(e => e.ModifiedByUser)
                .WithMany()
                .HasForeignKey(e => e.ModifiedByUserId)
                .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureSoftDeleteFilters(ModelBuilder modelBuilder)
    {
        // Configure global query filters for soft delete
        modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Customer>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Category>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Product>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<ProductVariant>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Supplier>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<SupplierProduct>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Sale>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<SaleItem>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Payment>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<InventoryMovement>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<UserSession>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Setting>().HasQueryFilter(e => !e.IsDeleted);
    }

    private void SeedInitialData(ModelBuilder modelBuilder)
    {
        // Seed default admin user
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "$2a$11$8gE7mQZqO5qO5qO5qO5qOuK8gE7mQZqO5qO5qO5qO5qOuK8gE7mQZ", // "admin123"
                FirstName = "System",
                LastName = "Administrator",
                Role = Core.Enums.UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );

        // Seed default category
        modelBuilder.Entity<Category>().HasData(
            new Category
            {
                Id = 1,
                Name = "General",
                Description = "Default category for products",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );

        // Seed default settings
        var defaultSettings = new[]
        {
            new Setting { Id = 1, Key = "CompanyName", Value = "SmartPOS Store", Category = "Company", Description = "Company name for receipts and reports" },
            new Setting { Id = 2, Key = "CompanyAddress", Value = "", Category = "Company", Description = "Company address for receipts" },
            new Setting { Id = 3, Key = "CompanyPhone", Value = "", Category = "Company", Description = "Company phone number" },
            new Setting { Id = 4, Key = "CompanyEmail", Value = "", Category = "Company", Description = "Company email address" },
            new Setting { Id = 5, Key = "Currency", Value = "USD", Category = "General", Description = "Default currency code" },
            new Setting { Id = 6, Key = "CurrencySymbol", Value = "$", Category = "General", Description = "Currency symbol" },
            new Setting { Id = 7, Key = "TaxRate", Value = "0.15", Category = "Tax", Description = "Default tax rate" },
            new Setting { Id = 8, Key = "TaxInclusive", Value = "false", Category = "Tax", Description = "Whether tax is inclusive in prices" },
            new Setting { Id = 9, Key = "ReceiptFooter", Value = "Thank you for your business!", Category = "Receipt", Description = "Footer text for receipts" },
            new Setting { Id = 10, Key = "AutoBackup", Value = "true", Category = "Backup", Description = "Enable automatic backups" }
        };

        foreach (var setting in defaultSettings)
        {
            setting.CreatedAt = DateTime.UtcNow;
            setting.UpdatedAt = DateTime.UtcNow;
        }

        modelBuilder.Entity<Setting>().HasData(defaultSettings);
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }
    }
}
