using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents system configuration settings
/// </summary>
public class Setting : BaseEntity
{
    /// <summary>
    /// Setting key/name
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Key { get; set; } = string.Empty;

    /// <summary>
    /// Setting value
    /// </summary>
    [StringLength(2000)]
    public string? Value { get; set; }

    /// <summary>
    /// Setting description
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Setting category for grouping
    /// </summary>
    [StringLength(50)]
    public string Category { get; set; } = "General";

    /// <summary>
    /// Data type of the setting value
    /// </summary>
    [StringLength(20)]
    public string DataType { get; set; } = "string";

    /// <summary>
    /// Default value for the setting
    /// </summary>
    [StringLength(2000)]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Indicates whether this setting is required
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// Indicates whether this setting is read-only
    /// </summary>
    public bool IsReadOnly { get; set; } = false;

    /// <summary>
    /// Display order for UI
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Validation rules (JSON format)
    /// </summary>
    [StringLength(1000)]
    public string? ValidationRules { get; set; }

    /// <summary>
    /// Possible values (for dropdown/select settings)
    /// </summary>
    [StringLength(2000)]
    public string? PossibleValues { get; set; }

    /// <summary>
    /// User ID who last modified this setting
    /// </summary>
    public int? ModifiedByUserId { get; set; }

    /// <summary>
    /// Date when setting was last modified
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    // Navigation properties
    /// <summary>
    /// User who last modified this setting
    /// </summary>
    public virtual User? ModifiedByUser { get; set; }
}
