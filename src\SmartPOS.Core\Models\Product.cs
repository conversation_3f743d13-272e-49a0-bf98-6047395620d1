using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a product in the inventory
/// </summary>
public class Product : BaseEntity
{
    /// <summary>
    /// Product name
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Product SKU (Stock Keeping Unit)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SKU { get; set; } = string.Empty;

    /// <summary>
    /// Product barcode
    /// </summary>
    [StringLength(50)]
    public string? Barcode { get; set; }

    /// <summary>
    /// Category ID this product belongs to
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// Cost price (what we paid for the product)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Selling price (what customer pays)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal SellingPrice { get; set; }

    /// <summary>
    /// Minimum stock level for reorder alerts
    /// </summary>
    public int MinimumStockLevel { get; set; } = 0;

    /// <summary>
    /// Current stock quantity
    /// </summary>
    public int CurrentStock { get; set; } = 0;

    /// <summary>
    /// Unit of measurement (piece, kg, liter, etc.)
    /// </summary>
    [StringLength(20)]
    public string Unit { get; set; } = "piece";

    /// <summary>
    /// Product weight in grams
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Product dimensions (length x width x height in cm)
    /// </summary>
    [StringLength(50)]
    public string? Dimensions { get; set; }

    /// <summary>
    /// Product image path
    /// </summary>
    [StringLength(255)]
    public string? ImagePath { get; set; }

    /// <summary>
    /// Indicates whether this product is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Indicates whether this product is trackable in inventory
    /// </summary>
    public bool IsTrackable { get; set; } = true;

    /// <summary>
    /// Indicates whether this product has expiry date
    /// </summary>
    public bool HasExpiryDate { get; set; } = false;

    /// <summary>
    /// Default expiry period in days
    /// </summary>
    public int? DefaultExpiryDays { get; set; }

    /// <summary>
    /// Tax rate for this product (if different from default)
    /// </summary>
    [Column(TypeName = "decimal(5,4)")]
    public decimal? TaxRate { get; set; }

    /// <summary>
    /// Additional notes about the product
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Profit margin percentage
    /// </summary>
    public decimal ProfitMargin => CostPrice > 0 ? ((SellingPrice - CostPrice) / CostPrice) * 100 : 0;

    /// <summary>
    /// Indicates whether stock is low (below minimum level)
    /// </summary>
    public bool IsLowStock => CurrentStock <= MinimumStockLevel;

    // Navigation properties
    /// <summary>
    /// Category this product belongs to
    /// </summary>
    public virtual Category Category { get; set; } = null!;

    /// <summary>
    /// Product variants (size, color, etc.)
    /// </summary>
    public virtual ICollection<ProductVariant> Variants { get; set; } = new List<ProductVariant>();

    /// <summary>
    /// Sale items for this product
    /// </summary>
    public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

    /// <summary>
    /// Inventory movements for this product
    /// </summary>
    public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();

    /// <summary>
    /// Supplier products (if product is purchased from suppliers)
    /// </summary>
    public virtual ICollection<SupplierProduct> SupplierProducts { get; set; } = new List<SupplierProduct>();
}
