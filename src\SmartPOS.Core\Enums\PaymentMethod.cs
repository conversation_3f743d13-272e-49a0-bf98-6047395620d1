namespace SmartPOS.Core.Enums;

/// <summary>
/// Defines the available payment methods in the system
/// </summary>
public enum PaymentMethod
{
    /// <summary>
    /// Cash payment
    /// </summary>
    Cash = 1,

    /// <summary>
    /// Credit or debit card payment
    /// </summary>
    Card = 2,

    /// <summary>
    /// Electronic wallet payment (PayPal, Apple Pay, etc.)
    /// </summary>
    EWallet = 3,

    /// <summary>
    /// Bank transfer
    /// </summary>
    BankTransfer = 4,

    /// <summary>
    /// Check payment
    /// </summary>
    Check = 5,

    /// <summary>
    /// Store credit or gift card
    /// </summary>
    StoreCredit = 6,

    /// <summary>
    /// Split payment using multiple methods
    /// </summary>
    Split = 7
}
