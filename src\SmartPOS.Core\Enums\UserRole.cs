namespace SmartPOS.Core.Enums;

/// <summary>
/// Defines the different user roles in the SmartPOS system
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Full system access - can manage users, settings, and all operations
    /// </summary>
    Admin = 1,

    /// <summary>
    /// Can perform sales, manage inventory, and view reports
    /// </summary>
    Manager = 2,

    /// <summary>
    /// Can only perform sales transactions
    /// </summary>
    Cashier = 3,

    /// <summary>
    /// Read-only access to view reports and data
    /// </summary>
    Viewer = 4
}
