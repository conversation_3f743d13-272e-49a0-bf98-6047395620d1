using System.ComponentModel.DataAnnotations;
using SmartPOS.Core.Enums;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a system user (admin, manager, cashier, etc.)
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// Unique username for login
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// User's email address
    /// </summary>
    [Required]
    [StringLength(100)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Hashed password
    /// </summary>
    [Required]
    [StringLength(255)]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    [Required]
    [StringLength(50)]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    [Required]
    [StringLength(50)]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's phone number
    /// </summary>
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// User's role in the system
    /// </summary>
    public UserRole Role { get; set; } = UserRole.Cashier;

    /// <summary>
    /// Indicates whether the user account is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Date and time of last login
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Date and time of last logout
    /// </summary>
    public DateTime? LastLogoutAt { get; set; }

    /// <summary>
    /// Number of failed login attempts
    /// </summary>
    public int FailedLoginAttempts { get; set; } = 0;

    /// <summary>
    /// Date and time when account was locked due to failed attempts
    /// </summary>
    public DateTime? LockedUntil { get; set; }

    /// <summary>
    /// User's profile picture path
    /// </summary>
    [StringLength(255)]
    public string? ProfilePicturePath { get; set; }

    /// <summary>
    /// Additional notes about the user
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Full name of the user
    /// </summary>
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// Indicates whether the account is currently locked
    /// </summary>
    public bool IsLocked => LockedUntil.HasValue && LockedUntil > DateTime.UtcNow;

    // Navigation properties
    /// <summary>
    /// Sales made by this user
    /// </summary>
    public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();

    /// <summary>
    /// User login sessions
    /// </summary>
    public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();
}
