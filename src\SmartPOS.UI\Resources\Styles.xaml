<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Card Styles -->
    <Style x:Key="SmartPOSCard" TargetType="materialDesign:Card">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
    </Style>

    <!-- Button Styles -->
    <Style x:Key="MenuButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="IconButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- TextBox Styles -->
    <Style x:Key="SearchTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="materialDesign:HintAssist.Hint" Value="Search..."/>
        <Setter Property="materialDesign:TextFieldAssist.HasLeadingIcon" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.LeadingIcon" Value="Magnify"/>
    </Style>

    <!-- DataGrid Styles -->
    <Style x:Key="SmartPOSDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="13 8 8 8"/>
        <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="8"/>
    </Style>

    <!-- ComboBox Styles -->
    <Style x:Key="SmartPOSComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="materialDesign:HintAssist.Hint" Value="Select..."/>
    </Style>

    <!-- Label Styles -->
    <Style x:Key="SectionHeader" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <Style x:Key="SubHeader" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <Style x:Key="CaptionText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
    </Style>

    <!-- Status Styles -->
    <Style x:Key="StatusSuccess" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="StatusWarning" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="StatusError" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Form Styles -->
    <Style x:Key="FormContainer" TargetType="StackPanel">
        <Setter Property="Margin" Value="16"/>
    </Style>

    <Style x:Key="FormRow" TargetType="Grid">
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <!-- Toolbar Styles -->
    <Style x:Key="Toolbar" TargetType="ToolBar" BasedOn="{StaticResource MaterialDesignToolBar}">
        <Setter Property="Margin" Value="0,0,0,8"/>
        <Setter Property="Padding" Value="8"/>
    </Style>

    <!-- Progress Styles -->
    <Style x:Key="SmartPOSProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="4"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <!-- Chip Styles -->
    <Style x:Key="StatusChip" TargetType="materialDesign:Chip">
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Dialog Styles -->
    <Style x:Key="DialogContent" TargetType="StackPanel">
        <Setter Property="Margin" Value="24"/>
        <Setter Property="MinWidth" Value="400"/>
    </Style>

    <!-- List Styles -->
    <Style x:Key="SmartPOSListBox" TargetType="ListBox" BasedOn="{StaticResource MaterialDesignListBox}">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="SelectionMode" Value="Single"/>
    </Style>

    <!-- Expander Styles -->
    <Style x:Key="MenuExpander" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
        <Setter Property="Margin" Value="8,4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

</ResourceDictionary>
