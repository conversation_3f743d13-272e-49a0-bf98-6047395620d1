using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents the relationship between suppliers and products with pricing
/// </summary>
public class SupplierProduct : BaseEntity
{
    /// <summary>
    /// Supplier ID
    /// </summary>
    public int SupplierId { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Supplier's product code or SKU
    /// </summary>
    [StringLength(50)]
    public string? SupplierProductCode { get; set; }

    /// <summary>
    /// Supplier's product name
    /// </summary>
    [StringLength(200)]
    public string? SupplierProductName { get; set; }

    /// <summary>
    /// Cost price from this supplier
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Minimum order quantity
    /// </summary>
    public int MinimumOrderQuantity { get; set; } = 1;

    /// <summary>
    /// Lead time in days
    /// </summary>
    public int LeadTimeDays { get; set; } = 0;

    /// <summary>
    /// Indicates whether this is the preferred supplier for this product
    /// </summary>
    public bool IsPreferred { get; set; } = false;

    /// <summary>
    /// Last order date from this supplier for this product
    /// </summary>
    public DateTime? LastOrderDate { get; set; }

    /// <summary>
    /// Last cost price paid
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? LastCostPrice { get; set; }

    /// <summary>
    /// Currency for pricing
    /// </summary>
    [StringLength(3)]
    public string Currency { get; set; } = "USD";

    /// <summary>
    /// Additional notes
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Indicates whether this supplier-product relationship is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Supplier
    /// </summary>
    public virtual Supplier Supplier { get; set; } = null!;

    /// <summary>
    /// Product
    /// </summary>
    public virtual Product Product { get; set; } = null!;
}
