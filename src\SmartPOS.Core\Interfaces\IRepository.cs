using System.Linq.Expressions;
using SmartPOS.Core.Models;

namespace SmartPOS.Core.Interfaces;

/// <summary>
/// Generic repository interface for basic CRUD operations
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public interface IRepository<T> where T : BaseEntity
{
    /// <summary>
    /// Get entity by ID
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <returns>Entity or null if not found</returns>
    Task<T?> GetByIdAsync(int id);

    /// <summary>
    /// Get all entities
    /// </summary>
    /// <returns>Collection of entities</returns>
    Task<IEnumerable<T>> GetAllAsync();

    /// <summary>
    /// Find entities by predicate
    /// </summary>
    /// <param name="predicate">Search predicate</param>
    /// <returns>Collection of matching entities</returns>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// Get first entity matching predicate
    /// </summary>
    /// <param name="predicate">Search predicate</param>
    /// <returns>First matching entity or null</returns>
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// Check if any entity matches predicate
    /// </summary>
    /// <param name="predicate">Search predicate</param>
    /// <returns>True if any entity matches</returns>
    Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// Count entities matching predicate
    /// </summary>
    /// <param name="predicate">Search predicate (optional)</param>
    /// <returns>Count of matching entities</returns>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);

    /// <summary>
    /// Get paged results
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="predicate">Filter predicate (optional)</param>
    /// <param name="orderBy">Order by expression (optional)</param>
    /// <param name="ascending">Sort order (default: true)</param>
    /// <returns>Paged result</returns>
    Task<PagedResult<T>> GetPagedAsync<TKey>(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, TKey>>? orderBy = null,
        bool ascending = true);

    /// <summary>
    /// Add new entity
    /// </summary>
    /// <param name="entity">Entity to add</param>
    /// <returns>Added entity</returns>
    Task<T> AddAsync(T entity);

    /// <summary>
    /// Add multiple entities
    /// </summary>
    /// <param name="entities">Entities to add</param>
    /// <returns>Task</returns>
    Task AddRangeAsync(IEnumerable<T> entities);

    /// <summary>
    /// Update entity
    /// </summary>
    /// <param name="entity">Entity to update</param>
    /// <returns>Updated entity</returns>
    Task<T> UpdateAsync(T entity);

    /// <summary>
    /// Update multiple entities
    /// </summary>
    /// <param name="entities">Entities to update</param>
    /// <returns>Task</returns>
    Task UpdateRangeAsync(IEnumerable<T> entities);

    /// <summary>
    /// Delete entity (soft delete)
    /// </summary>
    /// <param name="entity">Entity to delete</param>
    /// <returns>Task</returns>
    Task DeleteAsync(T entity);

    /// <summary>
    /// Delete entity by ID (soft delete)
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <returns>Task</returns>
    Task DeleteAsync(int id);

    /// <summary>
    /// Delete multiple entities (soft delete)
    /// </summary>
    /// <param name="entities">Entities to delete</param>
    /// <returns>Task</returns>
    Task DeleteRangeAsync(IEnumerable<T> entities);

    /// <summary>
    /// Permanently delete entity from database
    /// </summary>
    /// <param name="entity">Entity to permanently delete</param>
    /// <returns>Task</returns>
    Task HardDeleteAsync(T entity);

    /// <summary>
    /// Permanently delete entity by ID
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <returns>Task</returns>
    Task HardDeleteAsync(int id);

    /// <summary>
    /// Restore soft deleted entity
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <returns>Task</returns>
    Task RestoreAsync(int id);

    /// <summary>
    /// Get entities including soft deleted ones
    /// </summary>
    /// <returns>Collection of all entities including deleted</returns>
    Task<IEnumerable<T>> GetAllIncludingDeletedAsync();

    /// <summary>
    /// Get only soft deleted entities
    /// </summary>
    /// <returns>Collection of deleted entities</returns>
    Task<IEnumerable<T>> GetDeletedAsync();
}

/// <summary>
/// Represents a paged result
/// </summary>
/// <typeparam name="T">Entity type</typeparam>
public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
