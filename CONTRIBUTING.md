# Contributing to SmartPOS

Thank you for your interest in contributing to SmartPOS! This document provides guidelines and information for contributors.

## Table of Contents
- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Community](#community)

## Code of Conduct

This project and everyone participating in it is governed by our Code of Conduct. By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

### Our Pledge
- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Git
- Basic knowledge of C#, WPF, and Entity Framework

### Development Environment Setup
1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/yourusername/smartpos.git
   cd smartpos
   ```
3. Add the upstream remote:
   ```bash
   git remote add upstream https://github.com/smartpos/smartpos.git
   ```
4. Install dependencies:
   ```bash
   dotnet restore
   ```
5. Build the solution:
   ```bash
   dotnet build
   ```
6. Run tests to ensure everything works:
   ```bash
   dotnet test
   ```

## Contributing Guidelines

### Types of Contributions
We welcome various types of contributions:

#### 🐛 Bug Reports
- Use the bug report template
- Include steps to reproduce
- Provide system information
- Add screenshots if applicable

#### 💡 Feature Requests
- Use the feature request template
- Explain the use case
- Describe the expected behavior
- Consider implementation complexity

#### 📝 Documentation
- Fix typos and grammar
- Improve clarity and examples
- Add missing documentation
- Translate to other languages

#### 🔧 Code Contributions
- Bug fixes
- New features
- Performance improvements
- Code refactoring

### Before You Start
1. **Check existing issues** to avoid duplicates
2. **Discuss major changes** in an issue first
3. **Follow the coding standards** outlined below
4. **Write tests** for new functionality
5. **Update documentation** as needed

## Pull Request Process

### 1. Create a Branch
Create a descriptive branch name:
```bash
git checkout -b feature/add-customer-loyalty-program
git checkout -b bugfix/fix-inventory-calculation
git checkout -b docs/update-installation-guide
```

### 2. Make Your Changes
- Follow the coding standards
- Write clear, concise commit messages
- Add tests for new functionality
- Update documentation if needed

### 3. Test Your Changes
```bash
# Run all tests
dotnet test

# Run specific test categories
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration

# Check code coverage
dotnet test --collect:"XPlat Code Coverage"
```

### 4. Commit Your Changes
Use conventional commit messages:
```bash
git commit -m "feat: add customer loyalty program"
git commit -m "fix: resolve inventory calculation bug"
git commit -m "docs: update installation instructions"
```

### 5. Push and Create PR
```bash
git push origin your-branch-name
```
Then create a pull request on GitHub with:
- Clear title and description
- Reference related issues
- Include screenshots for UI changes
- List breaking changes if any

### 6. Code Review Process
- Maintainers will review your PR
- Address feedback promptly
- Make requested changes
- Keep the PR updated with main branch

## Coding Standards

### C# Coding Conventions
Follow Microsoft's C# coding conventions:

#### Naming Conventions
```csharp
// Classes, methods, properties - PascalCase
public class ProductService
{
    public async Task<Product> GetProductAsync(int id) { }
    public string ProductName { get; set; }
}

// Fields, parameters, local variables - camelCase
private readonly ILogger _logger;
public void ProcessOrder(int orderId)
{
    var orderItems = GetOrderItems(orderId);
}

// Constants - PascalCase
public const int MaxRetryAttempts = 3;

// Interfaces - IPascalCase
public interface IProductRepository { }
```

#### Code Organization
```csharp
// File structure
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using SmartPOS.Core.Models;

namespace SmartPOS.Services.Services
{
    /// <summary>
    /// Service for managing product operations
    /// </summary>
    public class ProductService : IProductService
    {
        // Fields first
        private readonly ILogger<ProductService> _logger;
        
        // Constructor
        public ProductService(ILogger<ProductService> logger)
        {
            _logger = logger;
        }
        
        // Public methods
        public async Task<Product> GetProductAsync(int id)
        {
            // Implementation
        }
        
        // Private methods
        private void ValidateProduct(Product product)
        {
            // Implementation
        }
    }
}
```

### XAML Conventions
```xml
<!-- Use proper indentation -->
<Window x:Class="SmartPOS.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="SmartPOS"
        Height="600" Width="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Use meaningful names -->
        <TextBlock x:Name="HeaderTextBlock"
                   Grid.Row="0"
                   Text="Welcome to SmartPOS"
                   Style="{StaticResource HeaderStyle}"/>
    </Grid>
</Window>
```

### Documentation Standards
```csharp
/// <summary>
/// Retrieves a product by its unique identifier
/// </summary>
/// <param name="id">The unique product identifier</param>
/// <returns>The product if found, otherwise null</returns>
/// <exception cref="ArgumentException">Thrown when id is less than or equal to zero</exception>
public async Task<Product?> GetProductByIdAsync(int id)
{
    if (id <= 0)
        throw new ArgumentException("Product ID must be greater than zero", nameof(id));
        
    return await _repository.GetByIdAsync(id);
}
```

## Testing Guidelines

### Test Structure
```csharp
[Fact]
public async Task GetProductByIdAsync_ValidId_ReturnsProduct()
{
    // Arrange
    var productId = 1;
    var expectedProduct = new Product { Id = productId, Name = "Test Product" };
    _mockRepository.Setup(r => r.GetByIdAsync(productId))
                   .ReturnsAsync(expectedProduct);
    
    // Act
    var result = await _productService.GetProductByIdAsync(productId);
    
    // Assert
    result.Should().NotBeNull();
    result.Id.Should().Be(productId);
    result.Name.Should().Be("Test Product");
}
```

### Test Categories
Use test categories to organize tests:
```csharp
[Fact]
[Trait("Category", "Unit")]
public void ValidateProduct_ValidProduct_ReturnsTrue() { }

[Fact]
[Trait("Category", "Integration")]
public async Task CreateProduct_ValidProduct_SavesToDatabase() { }
```

### Test Naming
- Use descriptive test names
- Format: `MethodName_Scenario_ExpectedResult`
- Be specific about the test case

## Documentation

### Code Documentation
- Add XML documentation for public APIs
- Include parameter descriptions
- Document exceptions
- Provide usage examples

### User Documentation
- Update user manual for new features
- Include screenshots for UI changes
- Provide step-by-step instructions
- Consider different user skill levels

### Technical Documentation
- Update architecture documentation
- Document design decisions
- Include diagrams where helpful
- Explain complex algorithms

## Community

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Email**: <EMAIL> for sensitive issues

### Getting Help
- Check existing documentation first
- Search closed issues for similar problems
- Ask questions in GitHub Discussions
- Be specific about your problem

### Helping Others
- Answer questions in discussions
- Review pull requests
- Improve documentation
- Share your experience

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation
- Special contributor badges

## Questions?

If you have questions about contributing, please:
1. Check this document first
2. Search existing issues and discussions
3. Create a new discussion if needed
4. Contact maintainers directly if necessary

Thank you for contributing to SmartPOS! 🎉
