<Window x:Class="SmartPOS.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="SmartPOS - Point of Sale System"
        Height="800" Width="1200"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Menu Bar -->
        <materialDesign:ColorZone Grid.Row="0" 
                                  Mode="PrimaryMid" 
                                  Padding="16"
                                  materialDesign:ElevationAssist.Elevation="Dp4">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon Kind="Store" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,16,0"/>
                    <TextBlock Text="SmartPOS" 
                             FontSize="24" 
                             FontWeight="Bold"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Style="{StaticResource MaterialDesignToolButton}"
                            ToolTip="Settings"
                            Margin="8,0">
                        <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignToolButton}"
                            ToolTip="User Profile"
                            Margin="8,0">
                        <materialDesign:PackIcon Kind="Account" Width="24" Height="24"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignToolButton}"
                            ToolTip="Logout"
                            Margin="8,0">
                        <materialDesign:PackIcon Kind="Logout" Width="24" Height="24"/>
                    </Button>
                </StackPanel>

                <TextBlock DockPanel.Dock="Right" 
                         Text="{Binding CurrentDateTime, StringFormat='MMM dd, yyyy - HH:mm'}"
                         VerticalAlignment="Center"
                         Margin="16,0"/>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- Main Content Area -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <materialDesign:ColorZone Grid.Column="0" 
                                      Mode="Light"
                                      Padding="0"
                                      materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Sales Section -->
                        <Expander Header="Sales" IsExpanded="True" Margin="8">
                            <StackPanel>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="New Sale"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Sales History"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- Inventory Section -->
                        <Expander Header="Inventory" Margin="8">
                            <StackPanel>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Package" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Products"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Tag" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Categories"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Suppliers"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- Customers Section -->
                        <Expander Header="Customers" Margin="8">
                            <StackPanel>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Customer List"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- Reports Section -->
                        <Expander Header="Reports" Margin="8">
                            <StackPanel>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Sales Reports"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ChartBar" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Inventory Reports"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>

                        <!-- Administration Section -->
                        <Expander Header="Administration" Margin="8">
                            <StackPanel>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountMultiple" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Users"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="16,8"
                                        Margin="0,2">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Cog" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="Settings"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:ColorZone>

            <!-- Content Area -->
            <Grid Grid.Column="1" Margin="16">
                <materialDesign:Card Padding="32" materialDesign:ElevationAssist.Elevation="Dp4">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="Store" 
                                               Width="128" 
                                               Height="128" 
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,0,32"/>
                        <TextBlock Text="Welcome to SmartPOS" 
                                 FontSize="32" 
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,16"/>
                        <TextBlock Text="Your comprehensive Point of Sale solution" 
                                 FontSize="16"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,32"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Content="Start New Sale"
                                Padding="24,12"
                                FontSize="16">
                            <Button.CommandParameter>
                                <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20"/>
                            </Button.CommandParameter>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" 
                                  Mode="PrimaryDark" 
                                  Padding="16,8"
                                  materialDesign:ElevationAssist.Elevation="Dp2">
            <DockPanel>
                <TextBlock Text="Ready" DockPanel.Dock="Left"/>
                <TextBlock Text="Database: Connected" DockPanel.Dock="Right"/>
                <TextBlock Text="User: Admin" DockPanel.Dock="Right" Margin="0,0,16,0"/>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>
