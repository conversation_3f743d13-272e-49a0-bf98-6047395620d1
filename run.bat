@echo off
echo Starting SmartPOS Application...

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo .NET SDK is not installed or not in PATH
    echo Please install .NET 8.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Navigate to UI project directory
cd src\SmartPOS.UI

REM Run the application
echo Running SmartPOS...
dotnet run

pause
