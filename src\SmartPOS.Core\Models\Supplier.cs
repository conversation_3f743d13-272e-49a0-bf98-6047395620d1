using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a supplier or vendor
/// </summary>
public class Supplier : BaseEntity
{
    /// <summary>
    /// Supplier name
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Contact person name
    /// </summary>
    [StringLength(100)]
    public string? ContactPerson { get; set; }

    /// <summary>
    /// Email address
    /// </summary>
    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Fax number
    /// </summary>
    [StringLength(20)]
    public string? FaxNumber { get; set; }

    /// <summary>
    /// Website URL
    /// </summary>
    [StringLength(200)]
    public string? Website { get; set; }

    /// <summary>
    /// Address line 1
    /// </summary>
    [StringLength(200)]
    public string? AddressLine1 { get; set; }

    /// <summary>
    /// Address line 2
    /// </summary>
    [StringLength(200)]
    public string? AddressLine2 { get; set; }

    /// <summary>
    /// City
    /// </summary>
    [StringLength(50)]
    public string? City { get; set; }

    /// <summary>
    /// State or province
    /// </summary>
    [StringLength(50)]
    public string? State { get; set; }

    /// <summary>
    /// Postal code
    /// </summary>
    [StringLength(20)]
    public string? PostalCode { get; set; }

    /// <summary>
    /// Country
    /// </summary>
    [StringLength(50)]
    public string? Country { get; set; }

    /// <summary>
    /// Tax identification number
    /// </summary>
    [StringLength(50)]
    public string? TaxId { get; set; }

    /// <summary>
    /// Payment terms (e.g., "Net 30", "COD")
    /// </summary>
    [StringLength(100)]
    public string? PaymentTerms { get; set; }

    /// <summary>
    /// Credit limit
    /// </summary>
    public decimal CreditLimit { get; set; } = 0;

    /// <summary>
    /// Current balance owed to supplier
    /// </summary>
    public decimal CurrentBalance { get; set; } = 0;

    /// <summary>
    /// Supplier rating (1-5 stars)
    /// </summary>
    public int Rating { get; set; } = 0;

    /// <summary>
    /// Additional notes about the supplier
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// Indicates whether supplier is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Date when supplier was first added
    /// </summary>
    public DateTime JoinDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date of last order from this supplier
    /// </summary>
    public DateTime? LastOrderDate { get; set; }

    /// <summary>
    /// Full address
    /// </summary>
    public string FullAddress
    {
        get
        {
            var address = new List<string>();
            if (!string.IsNullOrEmpty(AddressLine1)) address.Add(AddressLine1);
            if (!string.IsNullOrEmpty(AddressLine2)) address.Add(AddressLine2);
            if (!string.IsNullOrEmpty(City)) address.Add(City);
            if (!string.IsNullOrEmpty(State)) address.Add(State);
            if (!string.IsNullOrEmpty(PostalCode)) address.Add(PostalCode);
            if (!string.IsNullOrEmpty(Country)) address.Add(Country);
            return string.Join(", ", address);
        }
    }

    // Navigation properties
    /// <summary>
    /// Products supplied by this supplier
    /// </summary>
    public virtual ICollection<SupplierProduct> SupplierProducts { get; set; } = new List<SupplierProduct>();

    /// <summary>
    /// Inventory movements from this supplier
    /// </summary>
    public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
}
