# SmartPOS User Manual

## Table of Contents
1. [Getting Started](#getting-started)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [First Time Setup](#first-time-setup)
5. [User Interface Overview](#user-interface-overview)
6. [Sales Management](#sales-management)
7. [Inventory Management](#inventory-management)
8. [Customer Management](#customer-management)
9. [Reports](#reports)
10. [Settings](#settings)
11. [Troubleshooting](#troubleshooting)

## Getting Started

SmartPOS is a comprehensive Point of Sale system designed for small to medium-sized retail businesses. This manual will guide you through the installation, setup, and daily operations of the system.

### Key Features
- **Sales Processing**: Fast invoice generation with barcode scanning
- **Inventory Management**: Real-time stock tracking with automated alerts
- **Customer Management**: Customer profiles with purchase history
- **Multi-Payment Support**: Cash, card, e-wallet, and split payments
- **Reporting**: Comprehensive sales and inventory reports
- **Multi-Language**: English and Arabic support with RTL text
- **Offline Operation**: Works without internet connection

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10 (64-bit) or Windows 11
- **Processor**: Intel Core i3 or AMD equivalent
- **Memory**: 4 GB RAM
- **Storage**: 2 GB available disk space
- **Display**: 1024x768 resolution
- **Network**: Not required for basic operation

### Recommended Requirements
- **Operating System**: Windows 11 (64-bit)
- **Processor**: Intel Core i5 or AMD equivalent
- **Memory**: 8 GB RAM
- **Storage**: 10 GB available disk space (for data and backups)
- **Display**: 1920x1080 resolution or higher
- **Network**: Internet connection for cloud sync (optional)

### Additional Hardware
- **Barcode Scanner**: USB or Bluetooth compatible
- **Receipt Printer**: ESC/POS thermal printer (80mm)
- **Cash Drawer**: Compatible with receipt printer
- **Touch Screen**: Optional for enhanced user experience

## Installation

### Prerequisites
1. Download and install **.NET 8.0 Runtime** from Microsoft's official website
2. Ensure Windows is up to date
3. Have administrator privileges for installation

### Installation Steps
1. **Download SmartPOS**: Get the latest installer from the official source
2. **Run Installer**: Right-click the installer and select "Run as administrator"
3. **Follow Setup Wizard**:
   - Accept license agreement
   - Choose installation directory (default: `C:\Program Files\SmartPOS`)
   - Select additional components (desktop shortcut, start menu entry)
4. **Complete Installation**: Click "Install" and wait for completion
5. **First Launch**: The application will start automatically after installation

### Database Setup
SmartPOS uses SQLite database which is automatically created on first run:
- Database file location: `%APPDATA%\SmartPOS\smartpos.db`
- Automatic backup location: `%APPDATA%\SmartPOS\backups\`

## First Time Setup

### Initial Configuration
1. **Launch SmartPOS** from desktop shortcut or Start menu
2. **Default Login**: Use the default admin credentials:
   - Username: `admin`
   - Password: `admin123`
3. **Change Default Password**: Immediately change the default password for security

### Company Information
Navigate to **Settings > Company** and configure:
- Company Name
- Address
- Phone Number
- Email
- Tax ID (if applicable)
- Logo (for receipts)

### System Settings
Configure basic system settings:
- **Currency**: Set your local currency (USD, EUR, etc.)
- **Tax Rate**: Configure default tax percentage
- **Language**: Choose between English and Arabic
- **Theme**: Select Light or Dark theme

### Hardware Setup
1. **Receipt Printer**:
   - Connect printer via USB
   - Install printer drivers
   - Configure in Settings > Printing
   - Test print receipt

2. **Barcode Scanner**:
   - Connect scanner via USB or Bluetooth
   - Test scanning with sample barcode
   - Configure scan settings if needed

3. **Cash Drawer**:
   - Connect to receipt printer
   - Test opening mechanism

## User Interface Overview

### Main Window Layout
The SmartPOS interface consists of:

1. **Top Menu Bar**: Company logo, current date/time, user info, and system controls
2. **Navigation Panel** (Left): Main menu with expandable sections:
   - Sales (New Sale, Sales History)
   - Inventory (Products, Categories, Suppliers)
   - Customers (Customer List)
   - Reports (Sales Reports, Inventory Reports)
   - Administration (Users, Settings)
3. **Content Area** (Center): Main working area for selected function
4. **Status Bar** (Bottom): System status, database connection, current user

### Navigation
- Click on menu items to navigate between sections
- Use keyboard shortcuts for quick access
- Right-click for context menus where available

### Themes and Languages
- Switch themes: Settings > Appearance > Theme
- Change language: Settings > General > Language
- RTL support automatically enabled for Arabic

## Sales Management

### Creating a New Sale
1. Click **Sales > New Sale** from navigation menu
2. **Add Products**:
   - Scan barcode or search by name/SKU
   - Enter quantity
   - Apply discounts if needed
3. **Customer Selection** (Optional):
   - Search existing customer
   - Add new customer
   - Continue as walk-in customer
4. **Payment Processing**:
   - Select payment method (Cash, Card, E-wallet)
   - Enter payment amount
   - Process split payments if needed
5. **Complete Sale**:
   - Review transaction details
   - Print receipt
   - Open cash drawer (if applicable)

### Payment Methods
- **Cash**: Enter amount received, system calculates change
- **Card**: Process through integrated terminal or manual entry
- **E-Wallet**: Support for various digital payment methods
- **Split Payment**: Combine multiple payment methods
- **Store Credit**: Use customer loyalty points or gift cards

### Discounts and Promotions
- **Item Discount**: Apply discount to individual items
- **Transaction Discount**: Apply discount to entire sale
- **Percentage or Fixed Amount**: Choose discount type
- **Coupon Codes**: Enter promotional codes

### Receipt Management
- **Print Receipt**: Automatic or manual printing
- **Email Receipt**: Send receipt to customer email
- **Reprint**: Reprint previous receipts
- **Custom Footer**: Add promotional messages

## Inventory Management

### Product Management
1. **Add New Product**:
   - Navigate to Inventory > Products
   - Click "Add Product"
   - Fill required information:
     - Name, SKU, Barcode
     - Category, Cost Price, Selling Price
     - Stock quantity, Minimum level
     - Description, Images

2. **Product Categories**:
   - Organize products into categories
   - Create hierarchical category structure
   - Assign colors and icons for easy identification

3. **Product Variants**:
   - Add size, color, or style variants
   - Manage separate stock for each variant
   - Set different prices for variants

### Stock Management
1. **Stock Tracking**:
   - Real-time stock updates with each sale
   - Manual stock adjustments
   - Stock movement history

2. **Reorder Alerts**:
   - Set minimum stock levels
   - Automatic low stock notifications
   - Generate purchase orders

3. **Stock Takes**:
   - Perform periodic stock counts
   - Adjust discrepancies
   - Generate variance reports

### Supplier Management
1. **Add Suppliers**:
   - Company information
   - Contact details
   - Payment terms
   - Product associations

2. **Purchase Orders**:
   - Create purchase orders
   - Track deliveries
   - Update stock upon receipt

## Customer Management

### Customer Profiles
1. **Add New Customer**:
   - Personal information (Name, Phone, Email)
   - Address details
   - Preferences (Language, Marketing consent)

2. **Customer History**:
   - View purchase history
   - Track spending patterns
   - Loyalty points balance

3. **Loyalty Program**:
   - Earn points on purchases
   - Redeem points for discounts
   - Membership levels

## Reports

### Sales Reports
1. **Daily Sales Summary**:
   - Total sales, transactions count
   - Payment method breakdown
   - Top selling items

2. **Period Reports**:
   - Weekly, monthly, yearly summaries
   - Comparison with previous periods
   - Growth trends

3. **Product Performance**:
   - Best selling products
   - Slow moving items
   - Profit margins

### Inventory Reports
1. **Stock Status**:
   - Current stock levels
   - Low stock alerts
   - Out of stock items

2. **Stock Movements**:
   - Stock in/out transactions
   - Adjustment history
   - Supplier performance

3. **Valuation Reports**:
   - Inventory value at cost
   - Inventory value at selling price
   - Dead stock analysis

### Export Options
- **PDF**: Professional formatted reports
- **Excel**: Detailed data for analysis
- **Print**: Direct printing to receipt printer

## Settings

### General Settings
- Company information
- Currency and tax settings
- Language and regional settings
- Backup preferences

### User Management
1. **User Roles**:
   - Admin: Full system access
   - Manager: Sales, inventory, reports
   - Cashier: Sales transactions only
   - Viewer: Read-only access

2. **User Accounts**:
   - Create user accounts
   - Set permissions
   - Track user activity

### Hardware Configuration
- Receipt printer settings
- Barcode scanner configuration
- Cash drawer setup
- Display preferences

### Security Settings
- Password policies
- Session timeouts
- Audit logging
- Data encryption

## Troubleshooting

### Common Issues

#### Application Won't Start
- **Check .NET Runtime**: Ensure .NET 8.0 is installed
- **Run as Administrator**: Try running with elevated privileges
- **Check Database**: Verify database file isn't corrupted

#### Database Issues
- **Backup Restore**: Restore from automatic backup
- **Database Repair**: Use built-in repair tools
- **Contact Support**: For persistent issues

#### Printer Problems
- **Check Connection**: Verify USB/network connection
- **Driver Update**: Update printer drivers
- **Test Print**: Use Windows test print function

#### Performance Issues
- **Close Other Applications**: Free up system resources
- **Database Maintenance**: Run database optimization
- **Check Disk Space**: Ensure adequate free space

### Getting Help
- **Built-in Help**: Press F1 for context-sensitive help
- **User Manual**: This comprehensive guide
- **Video Tutorials**: Available online
- **Technical Support**: Contact support team

### Backup and Recovery
- **Automatic Backups**: Daily automatic database backups
- **Manual Backup**: Create backup before major changes
- **Restore Process**: Step-by-step recovery procedures

## Contact Information

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: ******-SMARTPOS
- **Website**: www.smartpos.com
- **Documentation**: docs.smartpos.com

---

*This manual is for SmartPOS Version 1.0. Please check for updates regularly.*
