using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a user login session for time tracking
/// </summary>
public class UserSession : BaseEntity
{
    /// <summary>
    /// User ID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// Session start time (login)
    /// </summary>
    public DateTime LoginTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Session end time (logout)
    /// </summary>
    public DateTime? LogoutTime { get; set; }

    /// <summary>
    /// IP address of the login
    /// </summary>
    [StringLength(45)]
    public string? IpAddress { get; set; }

    /// <summary>
    /// Computer/device name
    /// </summary>
    [StringLength(100)]
    public string? DeviceName { get; set; }

    /// <summary>
    /// Session token or identifier
    /// </summary>
    [StringLength(255)]
    public string? SessionToken { get; set; }

    /// <summary>
    /// Indicates whether session is still active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Reason for session end (logout, timeout, forced)
    /// </summary>
    [StringLength(50)]
    public string? EndReason { get; set; }

    /// <summary>
    /// Additional notes about the session
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Session duration in minutes
    /// </summary>
    public int? DurationMinutes
    {
        get
        {
            if (LogoutTime.HasValue)
                return (int)(LogoutTime.Value - LoginTime).TotalMinutes;
            return null;
        }
    }

    /// <summary>
    /// Current session duration (if still active)
    /// </summary>
    public int CurrentDurationMinutes => (int)(DateTime.UtcNow - LoginTime).TotalMinutes;

    // Navigation properties
    /// <summary>
    /// User this session belongs to
    /// </summary>
    public virtual User User { get; set; } = null!;
}
