using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SmartPOS.Core.Enums;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a payment transaction (supports split payments)
/// </summary>
public class Payment : BaseEntity
{
    /// <summary>
    /// Sale ID this payment belongs to
    /// </summary>
    public int SaleId { get; set; }

    /// <summary>
    /// Payment method used
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Amount paid using this payment method
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }

    /// <summary>
    /// Reference number for the payment (transaction ID, check number, etc.)
    /// </summary>
    [StringLength(100)]
    public string? Reference { get; set; }

    /// <summary>
    /// Card type (if payment method is card)
    /// </summary>
    [StringLength(50)]
    public string? CardType { get; set; }

    /// <summary>
    /// Last 4 digits of card number (if payment method is card)
    /// </summary>
    [StringLength(4)]
    public string? CardLastFour { get; set; }

    /// <summary>
    /// Authorization code (for card payments)
    /// </summary>
    [StringLength(50)]
    public string? AuthorizationCode { get; set; }

    /// <summary>
    /// Bank name (for bank transfers or checks)
    /// </summary>
    [StringLength(100)]
    public string? BankName { get; set; }

    /// <summary>
    /// E-wallet provider (PayPal, Apple Pay, etc.)
    /// </summary>
    [StringLength(50)]
    public string? EWalletProvider { get; set; }

    /// <summary>
    /// Currency code for this payment
    /// </summary>
    [StringLength(3)]
    public string Currency { get; set; } = "USD";

    /// <summary>
    /// Exchange rate (if different currency)
    /// </summary>
    [Column(TypeName = "decimal(18,6)")]
    public decimal ExchangeRate { get; set; } = 1.0m;

    /// <summary>
    /// Date and time when payment was processed
    /// </summary>
    public DateTime PaymentDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Indicates whether payment was successful
    /// </summary>
    public bool IsSuccessful { get; set; } = true;

    /// <summary>
    /// Error message if payment failed
    /// </summary>
    [StringLength(500)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional notes for this payment
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Indicates whether this payment was refunded
    /// </summary>
    public bool IsRefunded { get; set; } = false;

    /// <summary>
    /// Refunded amount (if partial refund)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal RefundedAmount { get; set; } = 0;

    /// <summary>
    /// Date and time when payment was refunded
    /// </summary>
    public DateTime? RefundedAt { get; set; }

    /// <summary>
    /// Refund reference number
    /// </summary>
    [StringLength(100)]
    public string? RefundReference { get; set; }

    /// <summary>
    /// Terminal or device ID where payment was processed
    /// </summary>
    [StringLength(50)]
    public string? TerminalId { get; set; }

    /// <summary>
    /// Batch number for card payments
    /// </summary>
    [StringLength(50)]
    public string? BatchNumber { get; set; }

    /// <summary>
    /// Receipt number for this payment
    /// </summary>
    [StringLength(50)]
    public string? ReceiptNumber { get; set; }

    // Navigation properties
    /// <summary>
    /// Sale this payment belongs to
    /// </summary>
    public virtual Sale Sale { get; set; } = null!;
}
