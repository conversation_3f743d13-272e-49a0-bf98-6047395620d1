using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a product category for organizing products
/// </summary>
public class Category : BaseEntity
{
    /// <summary>
    /// Category name
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Category description
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Category color for UI display (hex color code)
    /// </summary>
    [StringLength(7)]
    public string? Color { get; set; }

    /// <summary>
    /// Category icon path or name
    /// </summary>
    [StringLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Display order for sorting categories
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Parent category ID for hierarchical categories
    /// </summary>
    public int? ParentCategoryId { get; set; }

    /// <summary>
    /// Indicates whether this category is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Parent category
    /// </summary>
    public virtual Category? ParentCategory { get; set; }

    /// <summary>
    /// Child categories
    /// </summary>
    public virtual ICollection<Category> SubCategories { get; set; } = new List<Category>();

    /// <summary>
    /// Products in this category
    /// </summary>
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
