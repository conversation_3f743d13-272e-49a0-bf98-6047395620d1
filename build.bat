@echo off
echo Building SmartPOS Solution...

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo .NET SDK is not installed or not in PATH
    echo Please install .NET 8.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found. Building solution...

REM Restore NuGet packages
echo Restoring NuGet packages...
dotnet restore SmartPOS.sln
if %errorlevel% neq 0 (
    echo Failed to restore packages
    pause
    exit /b 1
)

REM Build solution
echo Building solution...
dotnet build SmartPOS.sln --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

REM Run tests
echo Running tests...
dotnet test tests/SmartPOS.Tests/SmartPOS.Tests.csproj --configuration Release --no-build --verbosity normal
if %errorlevel% neq 0 (
    echo Tests failed
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo To run the application:
echo   cd src\SmartPOS.UI
echo   dotnet run
echo.
pause
