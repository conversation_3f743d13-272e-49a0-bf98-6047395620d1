using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Unique identifier for the entity
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Date and time when the entity was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date and time when the entity was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID of the user who created this entity
    /// </summary>
    public int? CreatedById { get; set; }

    /// <summary>
    /// ID of the user who last updated this entity
    /// </summary>
    public int? UpdatedById { get; set; }

    /// <summary>
    /// Indicates whether this entity is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// Date and time when the entity was soft deleted
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    /// <summary>
    /// ID of the user who deleted this entity
    /// </summary>
    public int? DeletedById { get; set; }

    /// <summary>
    /// Navigation property for the user who created this entity
    /// </summary>
    public virtual User? CreatedBy { get; set; }

    /// <summary>
    /// Navigation property for the user who last updated this entity
    /// </summary>
    public virtual User? UpdatedBy { get; set; }

    /// <summary>
    /// Navigation property for the user who deleted this entity
    /// </summary>
    public virtual User? DeletedBy { get; set; }
}
