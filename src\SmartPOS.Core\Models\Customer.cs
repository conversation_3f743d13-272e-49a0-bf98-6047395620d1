using System.ComponentModel.DataAnnotations;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents a customer in the system
/// </summary>
public class Customer : BaseEntity
{
    /// <summary>
    /// Customer's first name
    /// </summary>
    [Required]
    [StringLength(50)]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Customer's last name
    /// </summary>
    [Required]
    [StringLength(50)]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Customer's email address
    /// </summary>
    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    /// <summary>
    /// Customer's phone number
    /// </summary>
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Customer's date of birth
    /// </summary>
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// Customer's gender
    /// </summary>
    [StringLength(10)]
    public string? Gender { get; set; }

    /// <summary>
    /// Customer's address line 1
    /// </summary>
    [StringLength(200)]
    public string? AddressLine1 { get; set; }

    /// <summary>
    /// Customer's address line 2
    /// </summary>
    [StringLength(200)]
    public string? AddressLine2 { get; set; }

    /// <summary>
    /// Customer's city
    /// </summary>
    [StringLength(50)]
    public string? City { get; set; }

    /// <summary>
    /// Customer's state or province
    /// </summary>
    [StringLength(50)]
    public string? State { get; set; }

    /// <summary>
    /// Customer's postal code
    /// </summary>
    [StringLength(20)]
    public string? PostalCode { get; set; }

    /// <summary>
    /// Customer's country
    /// </summary>
    [StringLength(50)]
    public string? Country { get; set; }

    /// <summary>
    /// Customer loyalty points
    /// </summary>
    public int LoyaltyPoints { get; set; } = 0;

    /// <summary>
    /// Customer's membership level
    /// </summary>
    [StringLength(20)]
    public string MembershipLevel { get; set; } = "Regular";

    /// <summary>
    /// Date when customer joined
    /// </summary>
    public DateTime JoinDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date of last purchase
    /// </summary>
    public DateTime? LastPurchaseDate { get; set; }

    /// <summary>
    /// Total amount spent by customer
    /// </summary>
    public decimal TotalSpent { get; set; } = 0;

    /// <summary>
    /// Number of purchases made by customer
    /// </summary>
    public int PurchaseCount { get; set; } = 0;

    /// <summary>
    /// Customer's preferred language
    /// </summary>
    [StringLength(5)]
    public string PreferredLanguage { get; set; } = "en";

    /// <summary>
    /// Additional notes about the customer
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Indicates whether customer wants to receive marketing emails
    /// </summary>
    public bool AcceptsMarketing { get; set; } = false;

    /// <summary>
    /// Customer's full name
    /// </summary>
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// Customer's full address
    /// </summary>
    public string FullAddress
    {
        get
        {
            var address = new List<string>();
            if (!string.IsNullOrEmpty(AddressLine1)) address.Add(AddressLine1);
            if (!string.IsNullOrEmpty(AddressLine2)) address.Add(AddressLine2);
            if (!string.IsNullOrEmpty(City)) address.Add(City);
            if (!string.IsNullOrEmpty(State)) address.Add(State);
            if (!string.IsNullOrEmpty(PostalCode)) address.Add(PostalCode);
            if (!string.IsNullOrEmpty(Country)) address.Add(Country);
            return string.Join(", ", address);
        }
    }

    // Navigation properties
    /// <summary>
    /// Sales made to this customer
    /// </summary>
    public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
}
