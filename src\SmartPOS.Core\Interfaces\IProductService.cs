using SmartPOS.Core.Models;

namespace SmartPOS.Core.Interfaces;

/// <summary>
/// Interface for product management service
/// </summary>
public interface IProductService
{
    // Basic CRUD operations
    Task<Product?> GetProductByIdAsync(int id);
    Task<Product?> GetProductBySkuAsync(string sku);
    Task<Product?> GetProductByBarcodeAsync(string barcode);
    Task<IEnumerable<Product>> GetAllProductsAsync();
    Task<IEnumerable<Product>> GetActiveProductsAsync();
    Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId);
    Task<PagedResult<Product>> GetProductsPagedAsync(int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null);
    
    Task<Product> CreateProductAsync(Product product);
    Task<Product> UpdateProductAsync(Product product);
    Task DeleteProductAsync(int id);
    Task RestoreProductAsync(int id);

    // Product variants
    Task<ProductVariant?> GetVariantByIdAsync(int id);
    Task<IEnumerable<ProductVariant>> GetProductVariantsAsync(int productId);
    Task<ProductVariant> CreateVariantAsync(ProductVariant variant);
    Task<ProductVariant> UpdateVariantAsync(ProductVariant variant);
    Task DeleteVariantAsync(int id);

    // Stock management
    Task<bool> IsProductInStockAsync(int productId, decimal quantity = 1);
    Task<bool> IsVariantInStockAsync(int variantId, decimal quantity = 1);
    Task UpdateStockAsync(int productId, int newStock, string reason, int userId);
    Task AdjustStockAsync(int productId, int adjustment, string reason, int userId);
    Task<IEnumerable<Product>> GetLowStockProductsAsync();
    Task<IEnumerable<Product>> GetOutOfStockProductsAsync();

    // Search and filtering
    Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);
    Task<IEnumerable<Product>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
    Task<IEnumerable<Product>> GetExpiringProductsAsync(int daysFromNow = 30);

    // Bulk operations
    Task<BulkOperationResult> BulkCreateProductsAsync(IEnumerable<Product> products);
    Task<BulkOperationResult> BulkUpdateProductsAsync(IEnumerable<Product> products);
    Task<BulkOperationResult> BulkDeleteProductsAsync(IEnumerable<int> productIds);

    // Import/Export
    Task<ImportResult> ImportProductsFromCsvAsync(Stream csvStream);
    Task<byte[]> ExportProductsToCsvAsync();
    Task<byte[]> ExportProductsToExcelAsync();

    // Image management
    Task<string> SaveProductImageAsync(int productId, Stream imageStream, string fileName);
    Task DeleteProductImageAsync(int productId);
    Task<string> SaveVariantImageAsync(int variantId, Stream imageStream, string fileName);
    Task DeleteVariantImageAsync(int variantId);

    // Validation
    Task<ValidationResult> ValidateProductAsync(Product product);
    Task<ValidationResult> ValidateVariantAsync(ProductVariant variant);
    Task<bool> IsSkuUniqueAsync(string sku, int? excludeProductId = null);
    Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeProductId = null);

    // Statistics
    Task<ProductStatistics> GetProductStatisticsAsync();
    Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
    Task<IEnumerable<Product>> GetSlowMovingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
}

/// <summary>
/// Result of bulk operations
/// </summary>
public class BulkOperationResult
{
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> Errors { get; set; } = new();
    public bool IsSuccess => FailureCount == 0;
}

/// <summary>
/// Result of import operations
/// </summary>
public class ImportResult
{
    public int TotalRows { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool IsSuccess => FailureCount == 0;
}

/// <summary>
/// Validation result
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Product statistics
/// </summary>
public class ProductStatistics
{
    public int TotalProducts { get; set; }
    public int ActiveProducts { get; set; }
    public int InactiveProducts { get; set; }
    public int LowStockProducts { get; set; }
    public int OutOfStockProducts { get; set; }
    public int ExpiringProducts { get; set; }
    public decimal TotalInventoryValue { get; set; }
    public int TotalCategories { get; set; }
    public int TotalVariants { get; set; }
}
