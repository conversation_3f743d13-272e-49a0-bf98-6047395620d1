using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SmartPOS.Core.Enums;

namespace SmartPOS.Core.Models;

/// <summary>
/// Represents an inventory movement (stock in/out, adjustments, etc.)
/// </summary>
public class InventoryMovement : BaseEntity
{
    /// <summary>
    /// Product ID
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Product variant ID (if applicable)
    /// </summary>
    public int? ProductVariantId { get; set; }

    /// <summary>
    /// Type of movement
    /// </summary>
    public InventoryMovementType MovementType { get; set; }

    /// <summary>
    /// Quantity moved (positive for stock in, negative for stock out)
    /// </summary>
    [Column(TypeName = "decimal(18,3)")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit cost at the time of movement
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitCost { get; set; } = 0;

    /// <summary>
    /// Total cost for this movement
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalCost { get; set; } = 0;

    /// <summary>
    /// Stock level before this movement
    /// </summary>
    public int StockBefore { get; set; }

    /// <summary>
    /// Stock level after this movement
    /// </summary>
    public int StockAfter { get; set; }

    /// <summary>
    /// Reference number (PO number, sale number, etc.)
    /// </summary>
    [StringLength(50)]
    public string? Reference { get; set; }

    /// <summary>
    /// Reason for the movement
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Expiry date for this batch (if applicable)
    /// </summary>
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// Batch number or lot number
    /// </summary>
    [StringLength(50)]
    public string? BatchNumber { get; set; }

    /// <summary>
    /// Supplier ID (for stock in movements)
    /// </summary>
    public int? SupplierId { get; set; }

    /// <summary>
    /// Sale ID (for stock out movements due to sales)
    /// </summary>
    public int? SaleId { get; set; }

    /// <summary>
    /// User who performed this movement
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// Date and time of the movement
    /// </summary>
    public DateTime MovementDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Indicates whether this movement has been processed
    /// </summary>
    public bool IsProcessed { get; set; } = true;

    /// <summary>
    /// Location or warehouse where movement occurred
    /// </summary>
    [StringLength(100)]
    public string? Location { get; set; }

    // Navigation properties
    /// <summary>
    /// Product this movement relates to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Product variant this movement relates to (if applicable)
    /// </summary>
    public virtual ProductVariant? ProductVariant { get; set; }

    /// <summary>
    /// Supplier (for stock in movements)
    /// </summary>
    public virtual Supplier? Supplier { get; set; }

    /// <summary>
    /// Sale (for stock out movements due to sales)
    /// </summary>
    public virtual Sale? Sale { get; set; }

    /// <summary>
    /// User who performed this movement
    /// </summary>
    public virtual User User { get; set; } = null!;
}
